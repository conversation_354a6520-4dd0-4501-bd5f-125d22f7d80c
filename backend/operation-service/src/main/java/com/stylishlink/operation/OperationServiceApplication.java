package com.stylishlink.operation;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 运营服务主应用类
 * 排除MongoDB自动配置，等需要时再开启
 */
@SpringBootApplication(
    exclude = {
        MongoAutoConfiguration.class,
        MongoDataAutoConfiguration.class
    },
    scanBasePackages = {
        "com.stylishlink.operation",
        "com.stylishlink.common"
    }
)
@EnableDiscoveryClient
@MapperScan("com.stylishlink.operation.mapper")
public class OperationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OperationServiceApplication.class, args);
    }
} 