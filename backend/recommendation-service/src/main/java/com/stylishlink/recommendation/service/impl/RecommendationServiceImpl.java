package com.stylishlink.recommendation.service.impl;

import com.stylishlink.recommendation.dto.request.DailyRecommendRequest;
import com.stylishlink.recommendation.dto.request.FeedbackRequest;
import com.stylishlink.recommendation.dto.request.AiRecommendationRequest;
import com.stylishlink.recommendation.dto.response.DailyRecommendationResponse;
import com.stylishlink.recommendation.dto.response.AiRecommendationResponse;
import com.stylishlink.recommendation.entity.DailyRecommendation;
import com.stylishlink.recommendation.repository.DailyRecommendationRepository;
import com.stylishlink.recommendation.repository.UserPreferenceRepository;
import com.stylishlink.recommendation.service.RecommendationService;
import com.stylishlink.recommendation.client.AIServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 推荐服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RecommendationServiceImpl implements RecommendationService {

    private final DailyRecommendationRepository dailyRecommendationRepository;
    private final UserPreferenceRepository userPreferenceRepository;
    private final AIServiceClient aiServiceClient;

    @Override
    public DailyRecommendationResponse getDailyRecommendation(DailyRecommendRequest request) {
        log.info("获取每日推荐: {}", request);
        
        try {
            // 获取当前用户ID (从上下文获取)
            Long userId = getCurrentUserId();
            LocalDate targetDate = request.getDate() != null ? request.getDate() : LocalDate.now();
            
            // 检查是否已存在当日推荐
            DailyRecommendation existing = dailyRecommendationRepository
                .selectByUserIdAndDate(userId, targetDate);
            
            if (existing != null && !Boolean.TRUE.equals(request.getForceRegenerate())) {
                // 返回已存在的推荐
                return convertToResponse(existing);
            }
            
            // 生成新的推荐
            DailyRecommendation recommendation = generateDailyRecommendation(userId, request);
            dailyRecommendationRepository.insert(recommendation);
            
            log.info("每日推荐生成成功: {}", recommendation.getId());
            return convertToResponse(recommendation);
            
        } catch (Exception e) {
            log.error("获取每日推荐失败: {}", request, e);
            throw new RuntimeException("获取每日推荐失败: " + e.getMessage());
        }
    }

    @Override
    public void submitFeedback(FeedbackRequest request) {
        log.info("提交推荐反馈: {}", request);
        
        try {
            // 查找推荐记录
            DailyRecommendation recommendation = dailyRecommendationRepository
                .selectById(request.getRecommendId());
            
            if (recommendation == null) {
                throw new RuntimeException("推荐记录不存在");
            }
            
            // 更新反馈信息
            DailyRecommendation.RecommendationFeedback feedback = DailyRecommendation.RecommendationFeedback.builder()
                .liked(request.getLiked())
                .rating(request.getRating())
                .comment(request.getFeedback())
                .createdAt(LocalDateTime.now())
                .build();
            
            recommendation.setFeedback(feedback);
            recommendation.setUpdatedAt(LocalDateTime.now());
            
            dailyRecommendationRepository.updateById(recommendation);
            log.info("推荐反馈提交成功: {}", request.getRecommendId());
            
        } catch (Exception e) {
            log.error("提交推荐反馈失败: {}", request, e);
            throw new RuntimeException("提交推荐反馈失败: " + e.getMessage());
        }
    }

    // 暂时返回null的方法，等后续创建对应的DTO后实现
    @Override
    public Object getOccasionRecommendation(Object request) {
        log.warn("场合推荐功能待实现");
        return null;
    }

    @Override
    public Object getMultiOccasionRecommendation(Object request) {
        log.warn("多场景推荐功能待实现");
        return null;
    }

    @Override
    public Object getShoppingRecommendation(Object request) {
        log.warn("购物推荐功能待实现");
        return null;
    }

    @Override
    public Object getTravelRecommendation(Object request) {
        log.warn("旅行推荐功能待实现");
        return null;
    }

    @Override
    public Object getPersonalizedRecommendation(Object request) {
        log.warn("个性化推荐功能待实现");
        return null;
    }

    @Override
    public Object getOutfitDetail(String outfitId) {
        log.warn("搭配详情功能待实现");
        return null;
    }

    @Override
    public Object getRecommendationHistory(Object request) {
        log.warn("推荐历史功能待实现");
        return null;
    }

    @Override
    public Object getRecommendationStats(Object request) {
        log.warn("推荐统计功能待实现");
        return null;
    }

    /**
     * 生成每日推荐
     */
    private DailyRecommendation generateDailyRecommendation(Long userId, DailyRecommendRequest request) {
        log.info("开始生成每日推荐，用户ID: {}, 请求: {}", userId, request);

        try {
            // 构建 AI 推荐请求
            AiRecommendationRequest aiRequest = buildAiRecommendationRequest(userId, request);

            // 调用 AI 服务生成推荐
            log.info("调用 AI 服务生成推荐");
            AiRecommendationResponse aiResponse = aiServiceClient.generateDailyRecommendation(aiRequest);

            // 将 AI 响应转换为数据库实体
            return DailyRecommendation.builder()
                .userId(userId)
                .date(request.getDate() != null ? request.getDate() : LocalDate.now())
                .reason(aiResponse.getReason() != null ? aiResponse.getReason() : "基于今日天气和您的五行分析生成的推荐")
                .totalScore(aiResponse.getTotalScore() != null ? aiResponse.getTotalScore() : 4.5)
                .multiScore(aiResponse.getMultiScore() != null ? aiResponse.getMultiScore() : Map.of(
                    "energy", 4.5,
                    "occasion", 4.2,
                    "style", 4.8,
                    "temperament", 4.3
                ))
                .isViewed(false)
                .build();

        } catch (Exception e) {
            log.error("调用 AI 服务失败", e);

            // 直接抛出异常，不提供兜底数据
            throw new RuntimeException("AI推荐服务调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换为响应DTO
     */
    private DailyRecommendationResponse convertToResponse(DailyRecommendation entity) {
        // TODO: 实现对象转换逻辑
        return DailyRecommendationResponse.builder()
            .id(entity.getId().toString())
            .date(entity.getDate())
            .reason(entity.getReason())
            .totalScore(entity.getTotalScore())
            .multiScore(entity.getMultiScore())
            .createdAt(entity.getCreatedAt())
            .build();
    }

    /**
     * 构建 AI 推荐请求
     */
    private AiRecommendationRequest buildAiRecommendationRequest(Long userId, DailyRecommendRequest request) {
        log.info("构建 AI 推荐请求，用户ID: {}", userId);

        // 构建天气信息
        AiRecommendationRequest.WeatherInfo weatherInfo = null;
        if (request.getWeather() != null) {
            weatherInfo = AiRecommendationRequest.WeatherInfo.builder()
                .temperature(request.getWeather().getTemperature())
                .condition(request.getWeather().getCondition())
                .humidity(request.getWeather().getHumidity())
                .uvIndex(request.getWeather().getUvIndex())
                .build();
        }

        // 构建用户偏好（临时使用默认值，后续从数据库获取）
        AiRecommendationRequest.UserPreferences userPreferences =
            AiRecommendationRequest.UserPreferences.builder()
                .favoriteStyles(List.of("简约", "商务"))
                .favoriteColors(List.of("白色", "蓝色", "黑色"))
                .dislikedStyles(List.of("嘻哈"))
                .dislikedColors(List.of("荧光色"))
                .height(170)
                .weight(60)
                .bodyShape("标准")
                .skinTone("偏白")
                .build();

        // 构建五行信息（临时使用默认值，后续实现五行计算）
        AiRecommendationRequest.WuxingInfo wuxingInfo =
            AiRecommendationRequest.WuxingInfo.builder()
                .baziString("庚午 戊子 甲寅 丙寅")
                .dayMaster("甲")
                .favorableElements(List.of("水", "木"))
                .unfavorableElements(List.of("火", "土"))
                .build();

        return AiRecommendationRequest.builder()
            .userId(userId)
            .date(request.getDate() != null ? request.getDate() : LocalDate.now())
            .occasions(request.getOccasions() != null ? request.getOccasions() : List.of("日常"))
            .weatherInfo(weatherInfo)
            .wuxingInfo(wuxingInfo)
            .userPreferences(userPreferences)
            .build();
    }

    /**
     * 获取当前用户ID（临时实现）
     */
    private Long getCurrentUserId() {
        // TODO: 从用户上下文获取当前用户ID
        return 1L; // 临时返回固定值
    }
} 