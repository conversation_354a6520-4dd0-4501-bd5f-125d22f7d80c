package com.stylishlink.recommendation.client;

import com.stylishlink.recommendation.dto.request.AiRecommendationRequest;
import com.stylishlink.recommendation.dto.response.AiRecommendationResponse;
import com.stylishlink.recommendation.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * AI服务客户端接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@FeignClient(
    name = "ai-service",
    url = "${ai-service.url:http://localhost:8084}",
    path = "/api/ai",
    fallbackFactory = AIServiceClientFallbackFactory.class,
    configuration = FeignConfig.class
)
public interface AIServiceClient {

    /**
     * 生成每日穿搭推荐
     * 
     * @param request AI推荐请求
     * @return AI推荐响应
     */
    @PostMapping("/recommendation/daily")
    AiRecommendationResponse generateDailyRecommendation(@RequestBody AiRecommendationRequest request);
} 