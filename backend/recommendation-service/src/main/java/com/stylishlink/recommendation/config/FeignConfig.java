package com.stylishlink.recommendation.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * Feign配置类
 */
@Configuration
@EnableFeignClients(basePackages = "com.stylishlink.recommendation.client")
public class FeignConfig {

    @Value("${feign.client.config.default.connectTimeout:10000}")
    private int connectTimeout;

    @Value("${feign.client.config.default.readTimeout:120000}")
    private int readTimeout;
    
    @Value("${feign.client.config.default.loggerLevel:BASIC}")
    private String loggerLevel;

    /**
     * 配置Feign日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.valueOf(loggerLevel.toUpperCase());
    }
    
    /**
     * 配置Feign请求超时
     */
    @Bean
    public Request.Options options() {
        return new Request.Options(
            connectTimeout, TimeUnit.MILLISECONDS,
            readTimeout, TimeUnit.MILLISECONDS,
            true
        );
    }
    
    /**
     * 配置Feign重试策略
     * AI 服务调用失败时不重试，避免浪费时间
     *
     * 使用 @Primary 确保这个配置优先级最高
     */
    @Bean
    @Primary
    public Retryer retryer() {
        return Retryer.NEVER_RETRY;  // 绝不重试
    }
} 