package com.stylishlink.recommendation.client;

import com.stylishlink.recommendation.dto.request.AiRecommendationRequest;
import com.stylishlink.recommendation.dto.response.AiRecommendationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * AI服务客户端降级工厂
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class AIServiceClientFallbackFactory implements FallbackFactory<AIServiceClient> {

    @Override
    public AIServiceClient create(Throwable cause) {
        return new AIServiceClient() {
            @Override
            public AiRecommendationResponse generateDailyRecommendation(AiRecommendationRequest request) {
                log.error("AI服务调用失败: {}", cause.getMessage(), cause);

                // 根据异常类型返回不同的错误信息
                String errorMessage = buildErrorMessage(cause);
                throw new RuntimeException(errorMessage, cause);
            }
        };
    }

    /**
     * 根据异常类型构建错误信息
     */
    private String buildErrorMessage(Throwable cause) {
        if (cause == null) {
            return "AI推荐服务调用失败，请稍后重试";
        }

        String message = cause.getMessage();
        if (message != null) {
            if (message.contains("timeout") || message.contains("timed out")) {
                return "AI推荐服务响应超时，请稍后重试";
            } else if (message.contains("Connection refused") || message.contains("connect")) {
                return "AI推荐服务暂时不可用，请稍后重试";
            } else if (message.contains("500") || message.contains("Internal Server Error")) {
                return "AI推荐服务内部错误，请稍后重试";
            } else if (message.contains("404") || message.contains("Not Found")) {
                return "AI推荐服务接口不存在，请联系技术支持";
            }
        }

        return "AI推荐服务调用失败: " + (message != null ? message : "未知错误");
    }


} 