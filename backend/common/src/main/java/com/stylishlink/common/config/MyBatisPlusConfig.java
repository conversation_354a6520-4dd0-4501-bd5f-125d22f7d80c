package com.stylishlink.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * MyBatis Plus 通用配置
 * 
 * 条件加载：
 * 1. 只有当 MyBatis-Plus 相关类存在时才加载
 * 2. 可通过配置 stylishlink.mybatis-plus.enabled=false 禁用
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@ConditionalOnClass({MybatisPlusInterceptor.class, MetaObjectHandler.class})
@ConditionalOnProperty(name = "stylishlink.mybatis-plus.enabled", havingValue = "true", matchIfMissing = true)
public class MyBatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 自动填充处理器
     * 自动填充 BaseEntity 中的审计字段
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                // 获取当前用户ID
                Long currentUserId = getCurrentUserId();
                
                // 自动填充插入时的字段
                this.strictInsertFill(metaObject, "createdBy", Long.class, currentUserId);
                this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updatedBy", Long.class, currentUserId);
                this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                // 获取当前用户ID
                Long currentUserId = getCurrentUserId();
                
                // 自动填充更新时的字段
                this.strictUpdateFill(metaObject, "updatedBy", Long.class, currentUserId);
                this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
            }
            
            /**
             * 获取当前用户ID
             * 
             * 优先级：
             * 1. 从 Spring Security 上下文获取
             * 2. 从请求头 X-User-Id 获取（网关传递）
             * 3. 默认返回系统用户ID: 0L
             */
            private Long getCurrentUserId() {
                try {
                    // TODO: 实现从用户上下文获取当前用户ID的逻辑
                    // 1. 尝试从 Spring Security 上下文获取
                    // SecurityContext context = SecurityContextHolder.getContext();
                    // if (context != null && context.getAuthentication() != null) {
                    //     Object principal = context.getAuthentication().getPrincipal();
                    //     if (principal instanceof String) {
                    //         return Long.parseLong((String) principal);
                    //     }
                    // }
                    
                    // 2. 尝试从请求头获取（网关传递的用户ID）
                    // HttpServletRequest request = getCurrentRequest();
                    // if (request != null) {
                    //     String userIdHeader = request.getHeader("X-User-Id");
                    //     if (StringUtils.hasText(userIdHeader)) {
                    //         return Long.parseLong(userIdHeader);
                    //     }
                    // }
                    
                    // 3. 默认返回系统用户ID
                    return 0L;
                    
                } catch (Exception e) {
                    // 如果获取用户ID失败，返回系统用户ID
                    return 0L;
                }
            }
            
            /**
             * 获取当前HTTP请求
             * 
             * private HttpServletRequest getCurrentRequest() {
             *     try {
             *         RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
             *         if (requestAttributes instanceof ServletRequestAttributes) {
             *             return ((ServletRequestAttributes) requestAttributes).getRequest();
             *         }
             *     } catch (Exception e) {
             *         // 忽略异常
             *     }
             *     return null;
             * }
             */
        };
    }
}
