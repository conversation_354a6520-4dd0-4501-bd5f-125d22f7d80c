# MyBatis-Plus 通用配置说明

## 概述

为了避免在各个微服务中重复配置 MyBatis-Plus，我们将通用的 MyBatis-Plus 配置提取到了 `common` 模块中。

## 配置位置

- **通用配置**：`backend/common/src/main/java/com/stylishlink/common/config/MyBatisPlusConfig.java`
- **特殊配置**：某些服务如 `user-service` 仍保留自己的配置（因为使用不同的字段名和数据类型）

## 自动加载条件

通用的 MyBatis-Plus 配置会在以下条件下自动加载：

1. **类路径检查**：`@ConditionalOnClass({MybatisPlusInterceptor.class, MetaObjectHandler.class})`
   - 只有当 MyBatis-Plus 相关类存在时才加载

2. **配置属性检查**：`@ConditionalOnProperty(name = "stylishlink.mybatis-plus.enabled", havingValue = "true", matchIfMissing = true)`
   - 默认启用（`matchIfMissing = true`）
   - 可通过配置 `stylishlink.mybatis-plus.enabled=false` 禁用

## 服务配置说明

### 使用通用配置的服务

以下服务会自动使用 common 模块中的 MyBatis-Plus 配置：

- **recommendation-service**：使用 `BaseEntity` 标准字段
- **ai-service**：使用 `BaseEntity` 标准字段
- **social-service**：使用 `BaseEntity` 标准字段
- **operation-service**：使用 `BaseEntity` 标准字段
- **file-service**：使用 `BaseEntity` 标准字段

### 禁用 MyBatis-Plus 的服务

以下服务不使用 MySQL，已禁用 MyBatis-Plus 配置：

- **gateway-service**：在 `application.yml` 中配置 `stylishlink.mybatis-plus.enabled: false`

### 使用自定义配置的服务

以下服务保留自己的 MyBatis-Plus 配置：

- **user-service**：使用 `createTime`/`updateTime` 字段和 `Date` 类型，与 `BaseEntity` 不兼容

## 自动填充字段

通用配置会自动填充以下字段（对应 `BaseEntity` 中的字段）：

### 插入时自动填充
- `createdBy`：创建人ID（当前用户ID）
- `createdAt`：创建时间（当前时间）
- `updatedBy`：更新人ID（当前用户ID）
- `updatedAt`：更新时间（当前时间）

### 更新时自动填充
- `updatedBy`：更新人ID（当前用户ID）
- `updatedAt`：更新时间（当前时间）

## 用户ID获取策略

当前用户ID的获取优先级：

1. **Spring Security 上下文**：从 `SecurityContextHolder` 获取（待实现）
2. **请求头**：从 `X-User-Id` 请求头获取（网关传递）（待实现）
3. **默认值**：系统用户ID `0L`

## 配置示例

### 启用 MyBatis-Plus（默认）
```yaml
# 不需要配置，默认启用
```

### 禁用 MyBatis-Plus
```yaml
stylishlink:
  mybatis-plus:
    enabled: false
```

## 注意事项

1. **字段命名**：确保实体类使用 `BaseEntity` 中定义的标准字段名
2. **数据类型**：确保使用 `LocalDateTime` 和 `Long` 类型
3. **条件加载**：不使用 MySQL 的服务应该禁用此配置
4. **用户上下文**：后续需要实现真正的用户上下文获取逻辑

## 包扫描配置

为了让服务能够加载 common 模块中的 MyBatis-Plus 配置，需要在主启动类中添加包扫描：

```java
@SpringBootApplication(scanBasePackages = {
    "com.stylishlink.服务名",
    "com.stylishlink.common"
})
```

### 已配置包扫描的服务

以下服务已正确配置包扫描：

- **recommendation-service** ✅
- **ai-service** ✅
- **social-service** ✅
- **operation-service** ✅
- **file-service** ✅
- **payment-service** ✅
- **wardrobe-service** ✅
- **user-service** ✅
- **gateway-service** ✅（已禁用 MyBatis-Plus）

## 迁移指南

如果要将现有服务迁移到使用通用配置：

1. 删除服务中的 `MyBatisPlusConfig.java` 文件
2. 在主启动类添加包扫描：`"com.stylishlink.common"`
3. 确保实体类继承 `BaseEntity` 或使用相同的字段名
4. 重新编译和测试

如果服务有特殊需求（如不同的字段名），保留自己的配置文件。
