package com.stylishlink.ai.client.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.service.FileUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 豆包视觉API客户端实现
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "spring.ai.doubao.vision.enabled", havingValue = "true")
public class DoubaoVisionClientImpl implements AiClient {

    @Value("${spring.ai.doubao.vision.api-key:}")
    private String apiKey;

    @Value("${spring.ai.doubao.vision.base-url:https://ark.cn-beijing.volces.com}")
    private String baseUrl;

    @Value("${spring.ai.doubao.vision.model:doubao-1-5-vision-pro-32k-250115}")
    private String model;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final FileUploadService fileUploadService;

    @Autowired
    public DoubaoVisionClientImpl(RestTemplate restTemplate, ObjectMapper objectMapper, FileUploadService fileUploadService) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
        this.fileUploadService = fileUploadService;
    }

    @Override
    public Map<String, Object> recognizeImage(MultipartFile imageFile, String recognitionType) {
        try {
            log.info("=== 豆包视觉API调用开始 ===");
            log.info("入参 - 文件名: {}, 文件大小: {} bytes, 文件类型: {}, 识别类型: {}",
                    imageFile.getOriginalFilename(), imageFile.getSize(), imageFile.getContentType(), recognitionType);
            log.info("配置 - API密钥: {}, 基础URL: {}, 模型: {}",
                    apiKey != null ? "已配置(长度:" + apiKey.length() + ")" : "未配置", baseUrl, model);

            // 1. 验证图片类型和大小
            validateImageFile(imageFile);

            // Determine FileCategory based on recognitionType
            String fileCategory= "TEMP_FILE";

            log.info("根据识别类型 '{}' 确定文件分类为: {}", recognitionType, fileCategory);

            // 2. 调用文件存储服务上传并保存文件，获取可访问的图片URL
            String actualImageUrl = uploadImageFile(imageFile, fileCategory);
            log.info("图片上传成功，URL: {}", actualImageUrl);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrl = new HashMap<>();
            imageUrl.put("url", actualImageUrl);
            imageContent.put("image_url", imageUrl);
            imageContent.put("type", "image_url");
            content.add(imageContent);
            
            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            String promptText = String.format(
                "请按以下步骤分析这张图片（识别类型：%s）：\n" +
                "\n" +
                "第一步：判断图片中是否包含衣物或配饰（如衣服、裤子、鞋子、包包、帽子、首饰等）\n" +
                "\n" +
                "第二步：如果包含衣物或配饰，请识别每一件物品的详细信息；如果不包含，请明确说明\n" +
                "\n" +
                "请严格按照以下JSON格式返回结果：\n" +
                "{\n" +
                "  \"hasClothingOrAccessory\": true/false,\n" +
                "  \"itemCount\": 识别到的物件数量,\n" +
                "  \"items\": [\n" +
                "    {\n" +
                "      \"type\": \"物件类型(如:上衣/裤子/鞋子/包包/帽子等)\",\n" +
                "      \"category\": \"具体类别(如:T恤/牛仔裤/运动鞋等)\",\n" +
                "      \"colors\": [\"颜色1\", \"颜色2\"],\n" +
                "      \"style\": \"风格描述(如:休闲/商务/运动/甜美等)\",\n" +
                "      \"materials\": [\"材质1\", \"材质2\"],\n" +
                "      \"patterns\": [\"图案1\", \"图案2\"],\n" +
                "      \"confidence\": 0.0-1.0的置信度数值\n" +
                "    }\n" +
                "  ],\n" +
                "  \"errorMessage\": \"错误信息(仅当hasClothingOrAccessory为false时填写)\"\n" +
                "}\n" +
                "\n" +
                "注意：\n" +
                "1. 所有描述请使用中文\n" +
                "2. 如果没有识别到衣物或配饰，hasClothingOrAccessory设为false，items为空数组，并在errorMessage中说明\"未识别到衣服配饰信息\"\n" +
                "3. 每件物品都要包含完整的信息", 
                recognitionType);
            textContent.put("text", promptText);
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            log.info("请求体结构: {}", requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.setBearerAuth(apiKey);
                log.info("已设置Bearer认证头");
            } else {
                log.warn("API密钥为空，可能导致认证失败");
            }

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            log.info("请求URL: {}", url);
            log.info("请求头: {}", headers);
            
            log.info("=== 发送HTTP请求 ===");
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();
            log.info("=== HTTP响应接收完成 ===");
            
            log.info("原始响应: {}", response);

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                log.info("响应中包含choices，数量: {}", choices.size());
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    String aiResponseContent = (String) messageObj.get("content");
                    result.put("aiResponse", aiResponseContent);
                    log.info("AI响应内容: {}", aiResponseContent);
                }
            } else {
                log.warn("响应中不包含choices字段或响应为空");
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
                log.info("Token使用情况: {}", usage);
            }

            result.put("model", model);
            // 添加真实的图片URL到返回结果
            result.put("imageUrl", actualImageUrl);
            
            log.info("=== 豆包视觉API调用结束 ===");
            log.info("最终返回结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("=== 豆包视觉API调用失败 ===");
            log.error("错误类型: {}", e.getClass().getSimpleName());
            log.error("错误信息: {}", e.getMessage());
            log.error("详细堆栈:", e);
            
            // 直接重新抛出异常，让Controller层处理并返回正确的错误响应
            throw new RuntimeException("图像识别失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> recognizeImageByUrl(String imageUrl, String recognitionType) {
        try {
            log.info("=== 豆包视觉API调用开始（URL方式）===");
            log.info("入参 - 图片URL: {}, 识别类型: {}", imageUrl, recognitionType);
            log.info("配置 - API密钥: {}, 基础URL: {}, 模型: {}",
                    apiKey != null ? "已配置(长度:" + apiKey.length() + ")" : "未配置", baseUrl, model);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrlObj = new HashMap<>();
            imageUrlObj.put("url", imageUrl);
            imageContent.put("image_url", imageUrlObj);
            imageContent.put("type", "image_url");
            content.add(imageContent);
            
            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            String promptText = String.format(
                "请按以下步骤分析这张图片（识别类型：%s）：\n" +
                "\n" +
                "第一步：判断图片中是否包含衣物或配饰（如衣服、裤子、鞋子、包包、帽子、首饰等）\n" +
                "\n" +
                "第二步：如果包含衣物或配饰，请识别每一件物品的详细信息；如果不包含，请明确说明\n" +
                "\n" +
                "请严格按照以下JSON格式返回结果：\n" +
                "{\n" +
                "  \"hasClothingOrAccessory\": true/false,\n" +
                "  \"itemCount\": 识别到的物件数量,\n" +
                "  \"items\": [\n" +
                "    {\n" +
                "      \"type\": \"物件类型(如:上衣/裤子/鞋子/包包/帽子等)\",\n" +
                "      \"category\": \"具体类别(如:T恤/牛仔裤/运动鞋等)\",\n" +
                "      \"colors\": [\"颜色1\", \"颜色2\"],\n" +
                "      \"style\": \"风格描述(如:休闲/商务/运动/甜美等)\",\n" +
                "      \"materials\": [\"材质1\", \"材质2\"],\n" +
                "      \"patterns\": [\"图案1\", \"图案2\"],\n" +
                "      \"confidence\": 0.0-1.0的置信度数值\n" +
                "    }\n" +
                "  ],\n" +
                "  \"errorMessage\": \"错误信息(仅当hasClothingOrAccessory为false时填写)\"\n" +
                "}\n" +
                "\n" +
                "注意：\n" +
                "1. 所有描述请使用中文\n" +
                "2. 如果没有识别到衣物或配饰，hasClothingOrAccessory设为false，items为空数组，并在errorMessage中说明\"未识别到衣服配饰信息\"\n" +
                "3. 每件物品都要包含完整的信息", 
                recognitionType);
            textContent.put("text", promptText);
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            log.info("请求体结构: {}", requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.setBearerAuth(apiKey);
                log.info("已设置Bearer认证头");
            } else {
                log.warn("API密钥为空，可能导致认证失败");
            }

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            log.info("请求URL: {}", url);
            log.info("请求头: {}", headers);
            
            log.info("=== 发送HTTP请求 ===");
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();
            log.info("=== HTTP响应接收完成 ===");
            
            log.info("原始响应: {}", response);

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                log.info("响应中包含choices，数量: {}", choices.size());
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    String aiResponseContent = (String) messageObj.get("content");
                    result.put("aiResponse", aiResponseContent);
                    log.info("AI响应内容: {}", aiResponseContent);
                }
            } else {
                log.warn("响应中不包含choices字段或响应为空");
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
                log.info("Token使用情况: {}", usage);
            }

            result.put("model", model);
            // 保留原始图片URL
            result.put("imageUrl", imageUrl);
            
            log.info("=== 豆包视觉API调用结束（URL方式）===");
            log.info("最终返回结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("=== 豆包视觉API调用失败（URL方式）===");
            log.error("错误类型: {}", e.getClass().getSimpleName());
            log.error("错误信息: {}", e.getMessage());
            log.error("详细堆栈:", e);
            
            // 直接重新抛出异常，让Controller层处理并返回正确的错误响应
            throw new RuntimeException("图像识别失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> analyzeStyle(String imageUrl) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrlObj = new HashMap<>();
            imageUrlObj.put("url", imageUrl);
            imageContent.put("image_url", imageUrlObj);
            imageContent.put("type", "image_url");
            content.add(imageContent);
            
            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("text", "请分析这张图片的服装风格。请返回详细的风格分析，包括：" +
                "风格分布、主导颜色、推荐风格、材质分析、图案分析、季节适应性、场合适应性等信息。");
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("aiResponse", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("豆包视觉风格分析完成，图片: {}, 结果: {}", imageUrl, result);
            return result;

        } catch (Exception e) {
            log.error("豆包视觉风格分析失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> analyzeColor(String imageUrl) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrlObj = new HashMap<>();
            imageUrlObj.put("url", imageUrl);
            imageContent.put("image_url", imageUrlObj);
            imageContent.put("type", "image_url");
            content.add(imageContent);
            
            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("text", "请分析这张图片的色彩搭配。请提供详细的色彩分析，包括：" +
                "主导颜色、色彩和谐度、季节适应性等信息。");
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("aiResponse", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("豆包视觉色彩分析完成，图片: {}, 结果: {}", imageUrl, result);
            return result;

        } catch (Exception e) {
            log.error("豆包视觉色彩分析失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> chat(String message, String context) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            
            // 添加系统消息
            if (context != null && !context.isEmpty()) {
                Map<String, Object> systemMessage = new HashMap<>();
                systemMessage.put("role", "system");
                systemMessage.put("content", context);
                messages.add(systemMessage);
            }
            
            // 添加用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", message);
            messages.add(userMessage);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("reply", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("豆包视觉对话完成，消息: {}, 回复: {}", message, result.get("reply"));
            return result;

        } catch (Exception e) {
            log.error("豆包视觉对话失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> evaluateAppearance(String[] imageUrls, String evaluationType) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URLs
            for (String imageUrl : imageUrls) {
                Map<String, Object> imageContent = new HashMap<>();
                Map<String, Object> imageUrlObj = new HashMap<>();
                imageUrlObj.put("url", imageUrl);
                imageContent.put("image_url", imageUrlObj);
                imageContent.put("type", "image_url");
                content.add(imageContent);
            }
            
            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("text", String.format("请对以下图片进行形象评估，评估类型：%s。" +
                "请提供详细的评估报告，包括整体评分、各项分数、改进建议等。", evaluationType));
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("aiResponse", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("豆包视觉形象评估完成，类型: {}, 结果: {}", evaluationType, result);
            return result;

        } catch (Exception e) {
            log.error("豆包视觉形象评估失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> rateOutfit(Map<String, Object> outfitData) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", String.format("请对以下穿搭进行评分分析：%s。" +
                "请提供详细的评分报告，包括AI评分、最终评分、各项细分评分、改进建议等。", outfitData.toString()));
            messages.add(message);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("aiResponse", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("豆包视觉穿搭评分完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("豆包视觉穿搭评分失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> analyzeBodyShape(MultipartFile imageFile) {
        try {
            log.info("=== 豆包身材识别API调用开始 ===");
            log.info("入参 - 文件名: {}, 文件大小: {} bytes, 文件类型: {}",
                    imageFile.getOriginalFilename(), imageFile.getSize(), imageFile.getContentType());

            // 1. 验证图片类型和大小
            validateImageFile(imageFile);

            // 2. 上传图片文件
            String actualImageUrl = uploadImageFile(imageFile, "TEMP_FILE");
            log.info("图片上传成功，URL: {}", actualImageUrl);

            // 3. 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);

            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();

            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrl = new HashMap<>();
            imageUrl.put("url", actualImageUrl);
            imageContent.put("image_url", imageUrl);
            imageContent.put("type", "image_url");
            content.add(imageContent);

            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            String promptText = "你是一个专业的身材分析师，请分析这张图片并按照以下步骤进行：\n" +
                "\n" +
                "第一步：判断这张图片是否为正面全身照\n" +
                "- 必须能看到人的全身（从头到脚）\n" +
                "- 人物必须是正面朝向或接近正面朝向\n" +
                "- 姿势相对自然，便于身材分析\n" +
                "\n" +
                "第二步：如果是正面全身照，请详细分析以下12个身材维度（每个维度用1-5的数值表示）：\n" +
                "1. 肩膀宽度：1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽\n" +
                "2. 腰型：1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显\n" +
                "3. 肚腩：1:没有, 2:略有小肚腩, 3:小肚腩, 4:偏大肚腩, 5:大肚腩\n" +
                "4. 臀型：1:下榻, 2:略有上翘, 3:正常, 4:较上翘, 5:上翘\n" +
                "5. 胯型：1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显\n" +
                "6. 臂长：1:短, 2:偏短, 3:正常, 4:偏长, 5:长\n" +
                "7. 臂围：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "8. 胯部宽度：1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽\n" +
                "9. 大腿：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "10. 小腿：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "11. 上下身粗细：1:上身粗, 2:偏上身粗, 3:匀称, 4:偏下身粗, 5:下身粗\n" +
                "12. 上下身长短：1:上身长, 2:偏上身长, 3:匀称, 4:偏下身长, 5:下身长\n" +
                "\n" +
                "第三步：基于分析结果，提供身材特征描述和穿搭建议\n" +
                "\n" +
                "请严格按照以下JSON格式返回结果，不允许任何格式变化：\n" +
                "{\n" +
                "  \"isFullBodyPhoto\": true,\n" +
                "  \"confidence\": 90,\n" +
                "  \"bodyType\": \"沙漏形\",\n" +
                "  \"bodyShape\": {\n" +
                "    \"shoulderWidth\": 3,\n" +
                "    \"waistShape\": 4,\n" +
                "    \"belly\": 1,\n" +
                "    \"hip\": 4,\n" +
                "    \"hipShape\": 4,\n" +
                "    \"armLength\": 3,\n" +
                "    \"armCircum\": 2,\n" +
                "    \"hipWidth\": 3,\n" +
                "    \"thigh\": 3,\n" +
                "    \"calf\": 2,\n" +
                "    \"bodyFat\": 3,\n" +
                "    \"bodyLength\": 3\n" +
                "  },\n" +
                "  \"analysis\": [\n" +
                "    {\n" +
                "      \"feature\": \"整体身材\",\n" +
                "      \"description\": \"身材分析描述\",\n" +
                "      \"type\": \"positive\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"suggestions\": [\n" +
                "    {\n" +
                "      \"category\": \"身材分析\",\n" +
                "      \"content\": \"身材特征分析内容\",\n" +
                "      \"priority\": \"high\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"category\": \"穿搭建议\",\n" +
                "      \"content\": \"具体穿搭建议内容\",\n" +
                "      \"priority\": \"high\"\n" +
                "    }\n" +
                "  ]\n" +
                "}\n" +
                "\n" +
                "如果不是正面全身照，则返回：\n" +
                "{\n" +
                "  \"isFullBodyPhoto\": false,\n" +
                "  \"confidence\": 0,\n" +
                "  \"reason\": \"具体原因说明\"\n" +
                "}\n" +
                "\n" +
                "重要要求：\n" +
                "1. 必须严格按照上述JSON格式返回，bodyShape必须是嵌套对象\n" +
                "2. analysis和suggestions必须是数组格式，不能是对象\n" +
                "3. 所有数值字段必须是数字类型，不能是字符串\n" +
                "4. confidence是0-100的整数，不是0-1的小数\n" +
                "5. 不要返回其他格式或添加额外字段\n" +
                "6. 分析要客观、专业，避免负面或敏感描述";

            textContent.put("text", promptText);
            textContent.put("type", "text");
            content.add(textContent);

            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);

            requestBody.put("messages", messages);

            log.info("身材识别请求体构建完成");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.setBearerAuth(apiKey);
                log.info("已设置Bearer认证头");
            } else {
                log.warn("API密钥为空，可能导致认证失败");
            }

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            log.info("请求URL: {}", url);

            log.info("=== 发送身材识别HTTP请求 ===");
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();
            log.info("=== 身材识别HTTP响应接收完成 ===");

            log.info("原始响应: {}", response);

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                log.info("响应中包含choices，数量: {}", choices.size());
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    String aiResponseContent = (String) messageObj.get("content");
                    result.put("aiResponse", aiResponseContent);
                    log.info("AI身材识别响应内容: {}", aiResponseContent);
                }
            } else {
                log.warn("响应中不包含choices字段或响应为空");
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
                log.info("Token使用情况: {}", usage);
            }

            result.put("model", model);
            // 添加真实的图片URL到返回结果
            result.put("imageUrl", actualImageUrl);

            log.info("=== 豆包身材识别API调用结束 ===");
            log.info("最终返回结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("=== 豆包身材识别API调用失败 ===");
            log.error("错误类型: {}", e.getClass().getSimpleName());
            log.error("错误信息: {}", e.getMessage());
            log.error("详细堆栈:", e);

            // 直接重新抛出异常，让Controller层处理并返回正确的错误响应
            throw new RuntimeException("身材识别失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> analyzeBodyShapeByUrl(String imageUrl) {
        try {
            log.info("开始身材识别分析（URL方式）: {}", imageUrl);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息内容
            ArrayList<Map<String, Object>> content = new ArrayList<>();
            
            // 添加图片URL
            Map<String, Object> imageContent = new HashMap<>();
            Map<String, Object> imageUrlMap = new HashMap<>();
            imageUrlMap.put("url", imageUrl);
            imageContent.put("image_url", imageUrlMap);
            imageContent.put("type", "image_url");
            content.add(imageContent);
            
            // 添加文本提示 - 使用与analyzeBodyShape相同的完整提示词
            Map<String, Object> textContent = new HashMap<>();
            String promptText = "你是一个专业的身材分析师，请分析这张图片并按照以下步骤进行：\n" +
                "\n" +
                "第一步：判断这张图片是否为正面全身照\n" +
                "- 必须能看到人的全身（从头到脚）\n" +
                "- 人物必须是正面朝向或接近正面朝向\n" +
                "- 姿势相对自然，便于身材分析\n" +
                "\n" +
                "第二步：如果是正面全身照，请详细分析以下12个身材维度（每个维度用1-5的数值表示）：\n" +
                "1. 肩膀宽度：1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽\n" +
                "2. 腰型：1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显\n" +
                "3. 肚腩：1:没有, 2:略有小肚腩, 3:小肚腩, 4:偏大肚腩, 5:大肚腩\n" +
                "4. 臀型：1:下榻, 2:略有上翘, 3:正常, 4:较上翘, 5:上翘\n" +
                "5. 胯型：1:直筒, 2:略有曲线, 3:有曲线, 4:曲线较明显, 5:曲线明显\n" +
                "6. 臂长：1:短, 2:偏短, 3:正常, 4:偏长, 5:长\n" +
                "7. 臂围：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "8. 胯部宽度：1:窄, 2:偏窄, 3:正常, 4:偏宽, 5:宽\n" +
                "9. 大腿：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "10. 小腿：1:细, 2:偏细, 3:正常, 4:偏粗, 5:粗\n" +
                "11. 上下身粗细：1:上身粗, 2:偏上身粗, 3:匀称, 4:偏下身粗, 5:下身粗\n" +
                "12. 上下身长短：1:上身长, 2:偏上身长, 3:匀称, 4:偏下身长, 5:下身长\n" +
                "\n" +
                "第三步：基于分析结果，提供身材特征描述和穿搭建议\n" +
                "\n" +
                "请严格按照以下JSON格式返回结果，不允许任何格式变化：\n" +
                "{\n" +
                "  \"isFullBodyPhoto\": true,\n" +
                "  \"confidence\": 90,\n" +
                "  \"bodyType\": \"沙漏形\",\n" +
                "  \"bodyShape\": {\n" +
                "    \"shoulderWidth\": 3,\n" +
                "    \"waistShape\": 4,\n" +
                "    \"belly\": 1,\n" +
                "    \"hip\": 4,\n" +
                "    \"hipShape\": 4,\n" +
                "    \"armLength\": 3,\n" +
                "    \"armCircum\": 2,\n" +
                "    \"hipWidth\": 3,\n" +
                "    \"thigh\": 3,\n" +
                "    \"calf\": 2,\n" +
                "    \"bodyFat\": 3,\n" +
                "    \"bodyLength\": 3\n" +
                "  },\n" +
                "  \"analysis\": [\n" +
                "    {\n" +
                "      \"feature\": \"整体身材\",\n" +
                "      \"description\": \"身材分析描述\",\n" +
                "      \"type\": \"positive\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"suggestions\": [\n" +
                "    {\n" +
                "      \"category\": \"身材分析\",\n" +
                "      \"content\": \"身材特征分析内容\",\n" +
                "      \"priority\": \"high\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"category\": \"穿搭建议\",\n" +
                "      \"content\": \"具体穿搭建议内容\",\n" +
                "      \"priority\": \"high\"\n" +
                "    }\n" +
                "  ]\n" +
                "}\n" +
                "\n" +
                "如果不是正面全身照，则返回：\n" +
                "{\n" +
                "  \"isFullBodyPhoto\": false,\n" +
                "  \"confidence\": 0,\n" +
                "  \"reason\": \"具体原因说明\"\n" +
                "}\n" +
                "\n" +
                "重要要求：\n" +
                "1. 必须严格按照上述JSON格式返回，bodyShape必须是嵌套对象\n" +
                "2. analysis和suggestions必须是数组格式，不能是对象\n" +
                "3. 所有数值字段必须是数字类型，不能是字符串\n" +
                "4. confidence是0-100的整数，不是0-1的小数\n" +
                "5. 不要返回其他格式或添加额外字段\n" +
                "6. 分析要客观、专业，避免负面或敏感描述";

            textContent.put("text", promptText);
            textContent.put("type", "text");
            content.add(textContent);
            
            // 构建消息
            ArrayList<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            
            requestBody.put("messages", messages);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            if (apiKey != null && !apiKey.isEmpty()) {
                headers.setBearerAuth(apiKey);
            }

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/api/v3/chat/completions";
            log.info("请求URL: {}", url);
            
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();
            log.info("豆包身材识别响应（URL方式）收到");

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                List<?> choices = (List<?>) response.get("choices");
                if (choices.size() > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    String aiResponseContent = (String) messageObj.get("content");
                    result.put("aiResponse", aiResponseContent);
                    log.info("豆包身材识别响应（URL方式）: {}", aiResponseContent);
                } else {
                    log.warn("响应中不包含choices字段或为空");
                }
            } else {
                log.warn("响应中不包含choices字段或响应为空");
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
                log.info("Token使用情况: {}", usage);
            }

            result.put("model", model);
            result.put("imageUrl", imageUrl);
            
            log.info("=== 豆包身材识别API调用结束（URL方式） ===");
            log.info("最终返回结果: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("身材识别失败（URL方式）", e);
            throw new RuntimeException("身材识别失败: " + e.getMessage());
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile imageFile) {
        if (imageFile == null || imageFile.isEmpty()) {
            throw new IllegalArgumentException("图片文件不能为空");
        }
        
        // 检查文件大小 (限制为10MB)
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (imageFile.getSize() > maxSize) {
            throw new IllegalArgumentException("图片文件大小不能超过10MB，当前大小: " + imageFile.getSize() + " bytes");
        }
        
        // 检查文件类型
        String contentType = imageFile.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IllegalArgumentException("只支持图片文件，当前类型: " + contentType);
        }
        
        // 支持的图片格式
        String[] supportedTypes = {"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"};
        boolean isSupported = false;
        for (String type : supportedTypes) {
            if (type.equals(contentType)) {
                isSupported = true;
                break;
            }
        }
        
        if (!isSupported) {
            throw new IllegalArgumentException("不支持的图片格式: " + contentType + 
                "，支持的格式: jpeg, jpg, png, gif, webp");
        }
        
        log.info("图片文件验证通过 - 文件名: {}, 大小: {} bytes, 类型: {}", 
                imageFile.getOriginalFilename(), imageFile.getSize(), contentType);
    }
    
    /**
     * 上传图片文件并返回可访问的URL
     * 通过FileUploadService调用file-service
     * @param imageFile 要上传的图片
     * @param category 计算得出的文件分类
     * @return 图片URL
     */
    private String uploadImageFile(MultipartFile imageFile, String category) {
        log.info("通过Feign客户端上传图片文件: {}, 类别: {}", imageFile.getOriginalFilename(), category);
        
        // 直接调用FileUploadService.uploadFile，传递动态 category
        String imageUrl = fileUploadService.uploadFile(imageFile, "ai-service", category, "PUBLIC");
        
        log.info("图片上传成功，获取到URL: {}", imageUrl);
        return imageUrl;
    }

    @Override
    public Map<String, Object> calculateEnergyInfo(Map<String, Object> request) {
        // 豆包客户端暂不支持能量计算，返回默认数据
        log.warn("豆包客户端暂不支持能量计算功能，返回默认数据");
        
        Map<String, Object> energyData = new HashMap<>();
        
        // 日期信息
        Map<String, Object> dateInfo = new HashMap<>();
        dateInfo.put("gregorian", request.get("date"));
        dateInfo.put("lunar", "农历信息");
        energyData.put("dateInfo", dateInfo);
        
        // 基本能量数据
        energyData.put("totalScore", 75);
        energyData.put("percentage", 60);
        energyData.put("peakTime", "上午8-10点");
        energyData.put("peakTimeDescription", "适合重要决策和创意工作");
        energyData.put("description", "今日能量平衡，整体运势平稳");
        
        // 五维能量评分
        Map<String, Object> dimensions = new HashMap<>();
        dimensions.put("love", 75);
        dimensions.put("career", 80);
        dimensions.put("wealth", 70);
        dimensions.put("health", 85);
        dimensions.put("relationship", 75);
        energyData.put("dimensions", dimensions);
        
        // 宜忌指南
        Map<String, Object> advice = new HashMap<>();
        advice.put("categories", new Object[]{});
        advice.put("lifeSuggestions", new Object[]{});
        energyData.put("advice", advice);
        
        // 幸运元素
        Map<String, Object> luckyElements = new HashMap<>();
        luckyElements.put("colors", new String[]{"#4ECDC4", "#FF6B6B"});
        luckyElements.put("clothing", new String[]{"推荐穿着舒适的服装"});
        luckyElements.put("accessories", new String[]{"简约的配饰"});
        luckyElements.put("makeup", new String[]{"清淡自然的妆容"});
        energyData.put("luckyElements", luckyElements);
        
        return energyData;
    }

    @Override
    public Map<String, Object> generateRecommendation(Map<String, Object> request) {
        log.warn("豆包视觉客户端暂不支持穿搭推荐功能，返回错误信息");
        throw new RuntimeException("豆包视觉客户端暂不支持穿搭推荐功能，请使用DeepSeek客户端");
    }
}