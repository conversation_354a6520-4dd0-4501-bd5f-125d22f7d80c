package com.stylishlink.ai.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * AI推荐响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI推荐响应")
public class AiRecommendationResponse {

    @Schema(description = "推荐搭配列表（3套）")
    private List<OutfitRecommendation> outfits;

    @Schema(description = "推荐理由")
    private String reason;

    @Schema(description = "五行分析")
    private WuxingAnalysis wuxingAnalysis;

    @Schema(description = "能量建议")
    private List<EnergyAdvice> energyAdvice;

    @Schema(description = "总评分", example = "4.5")
    private Double totalScore;

    @Schema(description = "多维度评分")
    private Map<String, Double> multiScore;

    /**
     * 搭配推荐
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "搭配推荐")
    public static class OutfitRecommendation {
        @Schema(description = "搭配名称", example = "清新商务套装")
        private String outfitName;

        @Schema(description = "搭配描述", example = "白色衬衫搭配藏青色西装裤")
        private String description;

        @Schema(description = "单品列表")
        private List<ClothingItem> items;

        @Schema(description = "搭配评分", example = "4.3")
        private Double score;

        @Schema(description = "适合场合", example = "[\"商务会议\", \"正式场合\"]")
        private List<String> suitableOccasions;
    }

    /**
     * 单品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "单品信息")
    public static class ClothingItem {
        @Schema(description = "单品ID", example = "shirt_001")
        private String itemId;

        @Schema(description = "单品名称", example = "白色长袖衬衫")
        private String itemName;

        @Schema(description = "单品类型", example = "上装")
        private String itemType;

        @Schema(description = "颜色", example = "白色")
        private String color;

        @Schema(description = "材质", example = "棉质")
        private String material;

        @Schema(description = "五行属性", example = "金")
        private String wuxingElement;

        @Schema(description = "单品描述（用于数据库匹配）", example = "白色纯棉长袖衬衫商务款")
        private String itemDescription;

        @Schema(description = "标签", example = "[\"商务\", \"基础款\", \"百搭\"]")
        private List<String> tags;

        @Schema(description = "推荐分数", example = "85")
        private Integer score;
    }

    /**
     * 五行分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "五行分析")
    public static class WuxingAnalysis {
        @Schema(description = "金", example = "40")
        private Integer metal;

        @Schema(description = "木", example = "10")
        private Integer wood;

        @Schema(description = "水", example = "20")
        private Integer water;

        @Schema(description = "火", example = "0")
        private Integer fire;

        @Schema(description = "土", example = "30")
        private Integer earth;

        @Schema(description = "五行整体解读", example = "此搭配以金(白色系)为主...")
        private String summary;

        @Schema(description = "各元素解析")
        private List<WuxingDetail> details;
    }

    /**
     * 五行详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "五行详情")
    public static class WuxingDetail {
        @Schema(description = "五行元素", example = "金")
        private String element;

        @Schema(description = "分值", example = "40")
        private Integer value;

        @Schema(description = "解读文本", example = "白色调衬衫和裙子...")
        private String summary;
    }

    /**
     * 能量建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "能量建议")
    public static class EnergyAdvice {
        @Schema(description = "五行元素", example = "木")
        private String element;

        @Schema(description = "建议文本", example = "搭配绿色或青色小饰品...")
        private String advice;
    }
}
