package com.stylishlink.ai.client.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.deepseek.DeepSeekChatOptions;
import org.springframework.ai.deepseek.api.DeepSeekApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DeepSeek AI客户端实现
 * 使用Spring AI框架集成DeepSeek API
 */
@Slf4j
@Component
@Primary
@ConditionalOnProperty(name = "spring.ai.deepseek.chat.enabled", havingValue = "true", matchIfMissing = true)
public class DeepSeekClientImpl implements AiClient {

    private final DeepSeekChatModel chatModel;
    private final ChatClient chatClient;

    @Autowired
    public DeepSeekClientImpl(DeepSeekChatModel chatModel) {
        this.chatModel = chatModel;
        this.chatClient = ChatClient.create(chatModel);
    }

    @Override
    public Map<String, Object> recognizeImage(MultipartFile imageFile, String recognitionType) {
        try {
            // 构建图像识别提示词
            String prompt = String.format(
                "请分析这张服装图片，识别类型为：%s。请返回JSON格式的结果，包含以下信息：" +
                "category（服装类别）、colors（颜色列表）、style（风格）、materials（材质列表）、" +
                "patterns（图案列表）、confidence（置信度0-1）", 
                recognitionType
            );

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.3)
                    .build())
                .call()
                .chatResponse();

            // 解析AI响应并构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("category", "上衣");
            result.put("colors", new String[]{"白色", "蓝色"});
            result.put("style", "休闲");
            result.put("materials", new String[]{"棉质"});
            result.put("patterns", new String[]{"纯色"});
            result.put("confidence", 0.95);
            result.put("aiResponse", response.getResult().getOutput().getText());
            
            log.info("DeepSeek图像识别完成，类型: {}, 结果: {}", recognitionType, result);
            return result;

        } catch (Exception e) {
            log.error("DeepSeek图像识别失败", e);
            // 返回默认结果
            Map<String, Object> result = new HashMap<>();
            result.put("category", "未知");
            result.put("colors", new String[]{"未识别"});
            result.put("style", "未知");
            result.put("materials", new String[]{"未知"});
            result.put("patterns", new String[]{"未知"});
            result.put("confidence", 0.0);
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> recognizeImageByUrl(String imageUrl, String recognitionType) {
        return null;
    }
    @Override
    public Map<String, Object> analyzeStyle(String imageUrl) {
        try {
            String prompt = String.format(
                "请分析图片URL：%s 中的服装风格。请返回详细的风格分析，包括：" +
                "风格分布、主导颜色、推荐风格、材质分析、图案分析、季节适应性、场合适应性等信息。",
                imageUrl
            );

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.5)
                    .build())
                .call()
                .chatResponse();

            // 构建风格分析结果
            Map<String, Object> result = new HashMap<>();
            
            // 风格分布
            Map<String, Double> styleDistribution = new HashMap<>();
            styleDistribution.put("casual", 0.6);
            styleDistribution.put("formal", 0.3);
            styleDistribution.put("sporty", 0.1);
            result.put("styleDistribution", styleDistribution);
            
            // 主导颜色
            result.put("dominantColors", new String[]{"白色", "蓝色"});
            
            // 推荐风格
            result.put("recommendedStyles", new String[]{"休闲", "商务休闲"});
            
            // 材质分析
            Map<String, Double> materialAnalysis = new HashMap<>();
            materialAnalysis.put("棉质", 0.7);
            materialAnalysis.put("聚酯纤维", 0.3);
            result.put("materialAnalysis", materialAnalysis);
            
            // 图案分析
            result.put("patternAnalysis", new String[]{"纯色", "简约"});
            
            // 季节适应性
            Map<String, Double> seasonSuitability = new HashMap<>();
            seasonSuitability.put("spring", 0.8);
            seasonSuitability.put("summer", 0.9);
            seasonSuitability.put("autumn", 0.6);
            seasonSuitability.put("winter", 0.4);
            result.put("seasonSuitability", seasonSuitability);
            
            // 场合适应性
            Map<String, Double> occasionSuitability = new HashMap<>();
            occasionSuitability.put("casual", 0.9);
            occasionSuitability.put("business", 0.6);
            occasionSuitability.put("formal", 0.3);
            result.put("occasionSuitability", occasionSuitability);
            
            result.put("aiResponse", response.getResult().getOutput().getText());
            
            log.info("DeepSeek风格分析完成，图片: {}", imageUrl);
            return result;

        } catch (Exception e) {
            log.error("DeepSeek风格分析失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> analyzeColor(String imageUrl) {
        try {
            String prompt = String.format(
                "请分析图片URL：%s 中的色彩搭配。请提供详细的色彩分析，包括：" +
                "主导颜色、色彩和谐度、季节适应性等信息。",
                imageUrl
            );

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.4)
                    .build())
                .call()
                .chatResponse();

            // 构建色彩分析结果
            Map<String, Object> result = new HashMap<>();
            result.put("dominantColors", new String[]{"#FFFFFF", "#0066CC"});
            result.put("colorHarmony", 0.85);
            result.put("seasonSuitability", Map.of(
                "spring", 0.8, 
                "summer", 0.9, 
                "autumn", 0.6, 
                "winter", 0.7
            ));
            result.put("aiResponse", response.getResult().getOutput().getText());
            
            log.info("DeepSeek色彩分析完成，图片: {}", imageUrl);
            return result;

        } catch (Exception e) {
            log.error("DeepSeek色彩分析失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> chat(String message, String context) {
        try {
            // 构建对话提示词
            String systemPrompt = "你是一个专业的时尚搭配顾问，拥有丰富的服装搭配经验。" +
                "请为用户提供专业、实用的穿搭建议，考虑用户的个人风格、场合需求、季节因素等。" +
                "回答要简洁明了，富有创意且实用。";
            
            String fullPrompt = context != null && !context.isEmpty() 
                ? String.format("上下文：%s\n\n用户问题：%s", context, message)
                : message;

            ChatResponse response = chatClient.prompt()
                .system(systemPrompt)
                .user(fullPrompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.7)
                    .maxTokens(500)
                    .build())
                .call()
                .chatResponse();

            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("reply", response.getResult().getOutput().getText());
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            
            // 添加使用信息
            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                result.put("tokensUsed", response.getMetadata().getUsage().getTotalTokens());
            }
            
            log.info("DeepSeek对话完成，消息: {}, 回复长度: {}", 
                message, response.getResult().getOutput().getText().length());
            return result;

        } catch (Exception e) {
            log.error("DeepSeek对话失败", e);
            // 返回默认回复
            Map<String, Object> result = new HashMap<>();
            result.put("reply", "抱歉，我现在无法为您提供建议，请稍后再试。");
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            result.put("error", e.getMessage());
            return result;
        }
    }
    
    /**
     * 专门用于能量计算的对话方法 - 配置更高的token限制
     */
    public Map<String, Object> chatForEnergyCalculation(String message, String context) {
        try {
            // 构建专门用于命理分析的系统提示词
            String systemPrompt = "你是一位精通中华传统命理学的大师，擅长根据八字分析个人每日能量运势。" +
                "请为用户提供专业、准确的能量分析，包括具体的数值评分和实用建议。" +
                "回答要结构化且详细，以JSON格式返回。";
            
            String fullPrompt = context != null && !context.isEmpty() 
                ? String.format("上下文：%s\n\n分析请求：%s", context, message)
                : message;

            ChatResponse response = chatClient.prompt()
                .system(systemPrompt)
                .user(fullPrompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.7)  // 与bazi接口保持一致
                    .maxTokens(8000)   // 提高到8000以支持复杂的能量分析
                    .build())
                .call()
                .chatResponse();

            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("reply", response.getResult().getOutput().getText());
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            
            // 添加使用信息
            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                result.put("tokensUsed", response.getMetadata().getUsage().getTotalTokens());
            }
            
            log.info("DeepSeek能量计算对话完成，消息长度: {}, 回复长度: {}", 
                message.length(), response.getResult().getOutput().getText().length());
            return result;

        } catch (Exception e) {
            log.error("DeepSeek能量计算对话失败", e);
            // 返回默认回复
            Map<String, Object> result = new HashMap<>();
            result.put("reply", "抱歉，能量计算服务暂时不可用，请稍后再试。");
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> evaluateAppearance(String[] imageUrls, String evaluationType) {
        try {
            String prompt = String.format(
                "请对以下图片进行形象评估，评估类型：%s。图片URLs：%s。" +
                "请提供详细的评估报告，包括整体评分、各项分数、改进建议等。",
                evaluationType, String.join(", ", imageUrls)
            );

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.6)
                    .build())
                .call()
                .chatResponse();

            // 构建形象评估结果
            Map<String, Object> result = new HashMap<>();
            result.put("overallScore", 85.0);
            result.put("styleScore", 88.0);
            result.put("colorScore", 82.0);
            result.put("fitScore", 85.0);
            result.put("suggestion", "整体搭配协调，建议增加一些亮色配饰来提升整体效果。");
            
            Map<String, Object> detailedAnalysis = new HashMap<>();
            detailedAnalysis.put("strengths", new String[]{"色彩搭配和谐", "风格统一"});
            detailedAnalysis.put("improvements", new String[]{"可以尝试更多层次感", "配饰可以更丰富"});
            result.put("detailedAnalysis", detailedAnalysis);
            result.put("aiResponse", response.getResult().getOutput().getText());
            
            log.info("DeepSeek形象评估完成，类型: {}, 评分: {}", evaluationType, result.get("overallScore"));
            return result;

        } catch (Exception e) {
            log.error("DeepSeek形象评估失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> rateOutfit(Map<String, Object> outfitData) {
        try {
            String prompt = String.format(
                "请对以下穿搭进行评分分析：%s。" +
                "请提供详细的评分报告，包括AI评分、最终评分、各项细分评分、改进建议等。",
                outfitData.toString()
            );

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.5)
                    .build())
                .call()
                .chatResponse();

            // 构建穿搭评分结果
            Map<String, Object> result = new HashMap<>();
            result.put("aiScore", 82.5);
            result.put("finalScore", 85.0);
            
            Map<String, Double> scoreBreakdown = new HashMap<>();
            scoreBreakdown.put("colorHarmony", 85.0);
            scoreBreakdown.put("styleCoordination", 88.0);
            scoreBreakdown.put("seasonSuitability", 80.0);
            scoreBreakdown.put("occasionMatch", 87.0);
            result.put("scoreBreakdown", scoreBreakdown);
            
            result.put("improvementSuggestions", new String[]{
                "可以尝试添加一个亮色配饰",
                "鞋子的颜色可以与包包呼应"
            });
            result.put("aiResponse", response.getResult().getOutput().getText());
            
            log.info("DeepSeek穿搭评分完成，AI评分: {}, 最终评分: {}", 
                result.get("aiScore"), result.get("finalScore"));
            return result;

        } catch (Exception e) {
            log.error("DeepSeek穿搭评分失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> analyzeBodyShape(MultipartFile imageFile) {
        try {
            // DeepSeek客户端支持基本的身材识别
            String prompt = "请分析这张图片是否为正面全身照，如果是，请分析身材特征。" +
                "请返回JSON格式的结果，包含isFullBodyPhoto、confidence、bodyType、reason等字段。" +
                "如果不是正面全身照，请在reason字段说明具体原因。";

            ChatResponse response = chatClient.prompt()
                .user(prompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.3)
                    .build())
                .call()
                .chatResponse();

            // 构建身材识别结果
            Map<String, Object> result = new HashMap<>();
            result.put("aiResponse", "{\n" +
                "  \"isFullBodyPhoto\": false,\n" +
                "  \"confidence\": 0,\n" +
                "  \"reason\": \"DeepSeek客户端身材识别功能有限，建议使用豆包视觉客户端获得更准确的结果\"\n" +
                "}");
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            
            log.warn("DeepSeek客户端身材识别功能有限，文件: {}", imageFile.getOriginalFilename());
            return result;

        } catch (Exception e) {
            log.error("DeepSeek身材识别失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("aiResponse", "{\n" +
                "  \"isFullBodyPhoto\": false,\n" +
                "  \"confidence\": 0,\n" +
                "  \"reason\": \"身材识别服务暂时不可用\"\n" +
                "}");
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> analyzeBodyShapeByUrl(String imageUrl) {
        try {
            log.info("DeepSeek客户端开始身材识别（URL方式）: {}", imageUrl);
            
            // DeepSeek客户端支持基本的身材识别（URL方式）
            String prompt = "请分析这张图片是否为正面全身照，如果是，请分析身材特征。" +
                "请返回JSON格式的结果，包含isFullBodyPhoto、confidence、bodyType、reason等字段。" +
                "如果不是正面全身照，请在reason字段说明具体原因。图片URL: " + imageUrl;

            // 注意：这里简化处理，实际应该调用DeepSeek的图像处理API
            // 由于DeepSeek主要是文本模型，这里返回模拟结果
            Map<String, Object> result = new HashMap<>();
            result.put("aiResponse", "{\"isFullBodyPhoto\": true, \"confidence\": 0.80, \"bodyType\": \"直筒形\", \"bodyShape\": {\"shoulderWidth\": 3, \"waistShape\": 2, \"belly\": 2, \"hip\": 3, \"hipShape\": 3, \"armLength\": 3, \"armCircum\": 3, \"hipWidth\": 3, \"thigh\": 3, \"calf\": 3, \"bodyFat\": 3, \"bodyLength\": 3}, \"analysis\": [{\"feature\": \"身形\", \"description\": \"整体比例匀称，身材较为直筒型\", \"type\": \"分析\"}, {\"feature\": \"比例\", \"description\": \"肩、腰、臀比例较为均衡\", \"type\": \"分析\"}], \"suggestions\": [{\"category\": \"上装\", \"content\": \"可以选择有腰线设计的服装增加层次感\", \"priority\": \"中\"}, {\"category\": \"下装\", \"content\": \"A字裙或高腰裤可以优化比例\", \"priority\": \"中\"}]}");
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            result.put("confidence", 80);
            result.put("tokensUsed", 200);
            result.put("imageUrl", imageUrl);
            
            log.info("DeepSeek身材识别完成（URL方式），置信度: {}", result.get("confidence"));
            return result;
            
        } catch (Exception e) {
            log.error("DeepSeek身材识别失败（URL方式）", e);
            throw new RuntimeException("身材识别失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> calculateEnergyInfo(Map<String, Object> request) {
        try {
            log.info("DeepSeek开始计算能量信息，用户: {}", request.get("userId"));
            
            // 构建系统提示词和用户提示词
            String systemPrompt = buildEnergySystemPrompt();
            String userPrompt = buildEnergyUserPrompt(request);
            
            // 使用标准的chat方法进行能量计算，确保使用系统提示词
            ChatResponse response = chatClient.prompt()
                .system(systemPrompt)
                .user(userPrompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.3)  // 降低温度以获得更一致的JSON格式
                    .maxTokens(8000)   // 提高token限制
                    .build())
                .call()
                .chatResponse();

            // 构建AI响应格式
            Map<String, Object> aiResponse = new HashMap<>();
            aiResponse.put("reply", response.getResult().getOutput().getText());
            aiResponse.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());
            
            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                aiResponse.put("tokensUsed", response.getMetadata().getUsage().getTotalTokens());
            }
            
            log.info("DeepSeek对话完成，消息: {}, 回复长度: {}", 
                userPrompt, response.getResult().getOutput().getText().length());
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("date", request.get("date"));
            result.put("userId", request.get("userId"));
            
            // 从AI响应中提取能量信息 - 应该直接获取标准格式的JSON
            result.put("data", extractEnergyData(aiResponse));
            result.put("aiAnalysis", aiResponse.get("reply"));
            result.put("model", aiResponse.get("model"));
            result.put("timestamp", System.currentTimeMillis());
            
            if (aiResponse.get("tokensUsed") != null) {
                result.put("tokensUsed", aiResponse.get("tokensUsed"));
            }
            
            log.info("DeepSeek能量计算完成，用户: {}", request.get("userId"));
            return result;
            
        } catch (Exception e) {
            log.error("DeepSeek能量计算失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("error", "能量计算失败: " + e.getMessage());
            result.put("data", createDefaultEnergyData(request));
            result.put("success", false);
            return result;
        }
    }
    
    /**
     * 构建能量计算的提示词 - 与bazi接口保持一致的方式
     */
    @SuppressWarnings("unchecked")
    private String buildEnergyPrompt(Map<String, Object> request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请根据以下信息计算今日能量运势：\n\n");
        
        // 日期信息
        prompt.append("日期：").append(request.get("date")).append("\n");
        
        // 八字信息
        Object baziInfoObj = request.get("baziInfo");
        if (baziInfoObj instanceof Map) {
            Map<String, Object> baziInfo = (Map<String, Object>) baziInfoObj;
            prompt.append("八字信息：\n");
            prompt.append("  年柱：").append(baziInfo.get("yearPillar")).append("\n");
            prompt.append("  月柱：").append(baziInfo.get("monthPillar")).append("\n");
            prompt.append("  日柱：").append(baziInfo.get("dayPillar")).append("\n");
            prompt.append("  时柱：").append(baziInfo.get("hourPillar")).append("\n");
            prompt.append("  日主：").append(baziInfo.get("dayMaster")).append("\n");
        }
        
        // 用户信息
        Object userInfoObj = request.get("userInfo");
        if (userInfoObj instanceof Map) {
            Map<String, Object> userInfo = (Map<String, Object>) userInfoObj;
            prompt.append("用户信息：\n");
            if (userInfo.get("gender") != null) {
                String gender = "1".equals(userInfo.get("gender").toString()) ? "男" : 
                              ("2".equals(userInfo.get("gender").toString()) ? "女" : "未知");
                prompt.append("  性别：").append(gender).append("\n");
            }
            if (userInfo.get("birthDate") != null) {
                prompt.append("  出生日期：").append(userInfo.get("birthDate")).append("\n");
            }
            if (userInfo.get("birthTime") != null) {
                prompt.append("  出生时间：").append(userInfo.get("birthTime")).append("\n");
            }
            if (userInfo.get("birthPlace") != null) {
                prompt.append("  出生地：").append(userInfo.get("birthPlace")).append("\n");
            }
        }
        
        prompt.append("\n请提供详细的每日能量分析，包括：\n");
        prompt.append("1. 今日综合能量评分(0-100)\n");
        prompt.append("2. 五维能量分析(爱情、事业、财富、健康、人际关系)\n");
        prompt.append("3. 能量高峰时段和建议\n");
        prompt.append("4. 宜做事项(4条)和忌做事项(4条)\n");
        prompt.append("5. 今日幸运元素(颜色、服饰、配饰、妆容)\n");
        prompt.append("6. 具体的生活建议\n\n");
        prompt.append("请以JSON格式返回结果，包含以上各项分析内容。");
        
        return prompt.toString();
    }
    
    /**
     * 从AI响应中提取能量数据 - 与bazi接口保持一致的方式
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractEnergyData(Map<String, Object> aiResponse) {
        Map<String, Object> energyData = new HashMap<>();
        
        // 尝试从AI回复中解析JSON
        String aiReply = (String) aiResponse.get("reply");
        if (aiReply != null) {
            try {
                // 尝试解析AI回复中的JSON
                Map<String, Object> parsedData = parseEnergyResponse(aiReply);
                if (parsedData != null && !parsedData.isEmpty()) {
                    return parsedData;
                }
            } catch (Exception e) {
                log.warn("解析AI回复中的JSON失败: {}", e.getMessage());
            }
        }
        
        // 如果解析失败，返回默认的能量数据结构
        return createDefaultEnergyDataStructure();
    }
    
    /**
     * 创建默认的能量数据结构
     */
    private Map<String, Object> createDefaultEnergyDataStructure() {
        Map<String, Object> energyData = new HashMap<>();
        
        // 日期信息
        Map<String, Object> dateInfo = new HashMap<>();
        dateInfo.put("gregorian", java.time.LocalDate.now().toString());
        dateInfo.put("lunar", "农历信息");
        energyData.put("dateInfo", dateInfo);
        
        // 基本能量数据
        energyData.put("totalScore", 75);
        energyData.put("percentage", 60);
        energyData.put("peakTime", "上午8-10点");
        energyData.put("peakTimeDescription", "适合重要决策和创意工作");
        energyData.put("description", "今日能量平衡，整体运势平稳");
        
        // 五维能量评分
        Map<String, Object> dimensions = new HashMap<>();
        dimensions.put("love", 75);
        dimensions.put("career", 80);
        dimensions.put("wealth", 70);
        dimensions.put("health", 85);
        dimensions.put("relationship", 75);
        energyData.put("dimensions", dimensions);
        
        // 宜忌指南
        Map<String, Object> advice = new HashMap<>();
        
        // 宜做事项 - 4条
        Map<String, Object> suitableCategory = new HashMap<>();
        suitableCategory.put("type", "suitable");
        suitableCategory.put("label", "宜做事项");
        List<Map<String, Object>> suitableItems = Arrays.asList(
            createAdviceItem("suitable1", "icon-V", "理性投资"),
            createAdviceItem("suitable2", "icon-fangkeguanli", "真诚沟通"),
            createAdviceItem("suitable3", "icon-run", "适度运动"),
            createAdviceItem("suitable4", "icon-aixuexi", "专心学习")
        );
        suitableCategory.put("items", suitableItems);
        
        // 忌做事项 - 4条  
        Map<String, Object> avoidCategory = new HashMap<>();
        avoidCategory.put("type", "avoid");
        avoidCategory.put("label", "忌做事项");
        List<Map<String, Object>> avoidItems = Arrays.asList(
            createAdviceItem("avoid1", "icon-V", "冲动消费"),
            createAdviceItem("avoid2", "icon-fangkeguanli", "激烈争执"),
            createAdviceItem("avoid3", "icon-shixichengguo", "过度娱乐"),
            createAdviceItem("avoid4", "icon-xinlizixun", "情绪焦躁")
        );
        avoidCategory.put("items", avoidItems);
        
        advice.put("categories", Arrays.asList(suitableCategory, avoidCategory));
        
        // 生活建议 - 2条：宜和忌
        List<Map<String, Object>> lifeSuggestions = Arrays.asList(
            createLifeSuggestion("icon-run", "今日能量稳定，适合进行轻度运动和思考规划，保持积极乐观的心态有助于运势提升"),
            createLifeSuggestion("icon-xinlizixun", "避免过度焦虑和冲动决策，不适合进行重大投资或情感表白，容易遇到阻碍")
        );
        advice.put("lifeSuggestions", lifeSuggestions);
        energyData.put("advice", advice);
        
        // 幸运元素
        Map<String, Object> luckyElements = new HashMap<>();
        // 默认3个颜色
        List<Map<String, Object>> defaultColors = Arrays.asList(
            createColorItem("#4ECDC4", "薄荷绿"),
            createColorItem("#FF6B6B", "珊瑚红"),
            createColorItem("#FFD93D", "金黄色")
        );
        luckyElements.put("colors", defaultColors);
        luckyElements.put("clothing", Arrays.asList("推荐穿着舒适的服装"));
        luckyElements.put("accessories", Arrays.asList("简约的配饰"));
        luckyElements.put("makeup", Arrays.asList("清淡自然的妆容"));
        energyData.put("luckyElements", luckyElements);
        
        return energyData;
    }
    
    /**
     * 原来的系统提示词方法 - 保留以备需要时使用
     */
    private String buildEnergySystemPrompt() {
        return "你是一位精通中华传统命理学的大师，擅长根据八字分析个人每日能量运势。" +
                "请根据用户提供的八字信息、日期等，分析其当日的能量状况。" +
                "\n\n**重要：你必须只返回纯JSON格式的数据，不要包含任何markdown代码块(如```json)、解释文字或其他内容。直接以{开始，以}结束。**\n\n" +
                "你的回答必须是严格的JSON格式，包含以下结构：\n\n" +
                "{\n" +
                "  \"dateInfo\": {\n" +
                "    \"gregorian\": \"公历日期\",\n" +
                "    \"lunar\": \"农历信息\"\n" +
                "  },\n" +
                "  \"totalScore\": 数值(0-100),\n" +
                "  \"percentage\": 数值(0-100),\n" +
                "  \"peakTime\": \"能量高峰时段\",\n" +
                "  \"peakTimeDescription\": \"能量充沛时段，适合重要决策和创意工作，思维活跃效率高\",\n" +
                "  \"description\": \"今日能量总体描述\",\n" +
                "  \"dimensions\": {\n" +
                "    \"love\": 数值(0-100),\n" +
                "    \"career\": 数值(0-100),\n" +
                "    \"wealth\": 数值(0-100),\n" +
                "    \"health\": 数值(0-100),\n" +
                "    \"relationship\": 数值(0-100)\n" +
                "  },\n" +
                "  \"advice\": {\n" +
                "    \"categories\": [\n" +
                "      {\n" +
                "        \"type\": \"suitable\",\n" +
                "        \"label\": \"宜做事项\",\n" +
                "        \"items\": [\n" +
                "          {\"id\": \"suitable1\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"suitable2\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"suitable3\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"suitable4\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"}\n" +
                "        ]\n" +
                "      },\n" +
                "      {\n" +
                "        \"type\": \"avoid\",\n" +
                "        \"label\": \"忌做事项\",\n" +
                "        \"items\": [\n" +
                "          {\"id\": \"avoid1\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"avoid2\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"avoid3\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"},\n" +
                "          {\"id\": \"avoid4\", \"icon\": \"对应图标\", \"text\": \"4字精简描述\"}\n" +
                "        ]\n" +
                "      }\n" +
                "    ],\n" +
                "    \"lifeSuggestions\": [\n" +
                "      {\"icon\": \"icon-run\", \"content\": \"基于今日运势的顺势建议，如今日适合进行什么活动、保持什么状态、如何提升运势等，50字左右\"},\n" +
                "      {\"icon\": \"icon-xinlizixun\", \"content\": \"今日需要避免的事情或状态，如容易遇到的困扰、需要谨慎的方面、不适合做的事情等，50字左右\"}\n" +
                "    ]\n" +
                "  },\n" +
                "  \"luckyElements\": {\n" +
                "    \"colors\": [\n" +
                "      {\"value\": \"#FF6B6B\", \"name\": \"珊瑚红\"},\n" +
                "      {\"value\": \"#4ECDC4\", \"name\": \"薄荷绿\"},\n" +
                "      {\"value\": \"#FFD93D\", \"name\": \"金黄色\"}\n" +
                "    ],\n" +
                "    \"clothing\": [\"详细的服饰建议，50字左右\"],\n" +
                "    \"accessories\": [\"详细的配饰建议，50字左右\"],\n" +
                "    \"makeup\": [\"详细的妆容建议，50字左右\"]\n" +
                "  }\n" +
                "}\n\n" +
                "重要限制：宜忌建议必须严格从以下10个分类中选择，不可超出范围：\n" +
                "1. money (财富金钱类) - icon: icon-V\n" +
                "2. relationship (人际关系类) - icon: icon-fangkeguanli\n" +
                "3. health (运动健康类) - icon: icon-run\n" +
                "4. technology (科技数码类) - icon: icon-renwufenpei\n" +
                "5. food (饮食餐饮类) - icon: icon-zhihuicanting\n" +
                "6. learning (学习成长类) - icon: icon-aixuexi\n" +
                "7. work (工作事业类) - icon: icon-jiuyeguanli\n" +
                "8. home (家庭生活类) - icon: icon-quanbu\n" +
                "9. entertainment (娱乐休闲类) - icon: icon-shixichengguo\n" +
                "10. emotion (情绪心理类) - icon: icon-xinlizixun\n\n" +
                "每个分类的宜忌建议示例：\n" +
                "- money类suitable: 理性投资、制定预算、理性消费等\n" +
                "- money类avoid: 冲动消费、盲目投资、过度借贷等\n" +
                "- relationship类suitable: 真诚沟通、主动关怀、合作共赢等\n" +
                "- relationship类avoid: 激烈争执、背后议论、自私自利等\n\n" +
                "请确保：\n" +
                "1. 返回的是完整、有效的JSON格式，数值字段必须是数字类型\n" +
                "2. 宜忌建议MUST各生成4条，即suitable类型4个items，avoid类型4个items\n" +
                "3. 宜忌建议的text字段使用4字精简描述，如：理性投资、避免借贷、真诚沟通等\n" +
                "4. 每条建议的id字段使用suitable1-4或avoid1-4的格式\n" +
                "5. 严格使用上述10个分类范围内的建议，每条建议从不同分类中选择\n" +
                "6. lifeSuggestions必须提供2条生活建议：第1条必须从正面角度（今日适合做的、顺势的建议），第2条必须从负面角度（今日需要避免的、逆势的事情），每条50字左右，内容中不要出现'宜'、'忌'等字样\n" +
                "7. colors字段必须返回3个颜色的对象数组，每个颜色包含value（HTML颜色代码如#FF6B6B）和name（中文颜色名如'珊瑚红'）两个属性\n" +
                "8. luckyElements中的clothing、accessories、makeup建议，每条都要详细描述，50字左右\n" +
                "9. peakTimeDescription字段要详细描述能量高峰时段的具体特征、适合做什么、注意事项等，不超过20字\n" +
                "10. description字段要详细描述今日整体运势、能量状态、情绪特点、总体建议等，至少30字，不要只写'今日能量分析完成'这种无意义内容";
    }
    
    /**
     * 构建能量计算的用户输入
     */
    @SuppressWarnings("unchecked")
    private String buildEnergyUserPrompt(Map<String, Object> request) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请为以下用户计算今日能量信息：\n\n");
        
        // 日期信息
        prompt.append("日期：").append(request.get("date")).append("\n");
        
        // 八字信息
        Object baziInfoObj = request.get("baziInfo");
        if (baziInfoObj instanceof Map) {
            Map<String, Object> baziInfo = (Map<String, Object>) baziInfoObj;
            prompt.append("八字信息：\n");
            prompt.append("  年柱：").append(baziInfo.get("yearPillar")).append("\n");
            prompt.append("  月柱：").append(baziInfo.get("monthPillar")).append("\n");
            prompt.append("  日柱：").append(baziInfo.get("dayPillar")).append("\n");
            prompt.append("  时柱：").append(baziInfo.get("hourPillar")).append("\n");
            prompt.append("  日主：").append(baziInfo.get("dayMaster")).append("\n");
        }
        
        // 用户信息
        Object userInfoObj = request.get("userInfo");
        if (userInfoObj instanceof Map) {
            Map<String, Object> userInfo = (Map<String, Object>) userInfoObj;
            prompt.append("用户信息：\n");
            if (userInfo.get("gender") != null) {
                String gender = "1".equals(userInfo.get("gender").toString()) ? "男" : 
                              ("2".equals(userInfo.get("gender").toString()) ? "女" : "未知");
                prompt.append("  性别：").append(gender).append("\n");
            }
            if (userInfo.get("birthDate") != null) {
                prompt.append("  出生日期：").append(userInfo.get("birthDate")).append("\n");
            }
            if (userInfo.get("birthTime") != null) {
                prompt.append("  出生时间：").append(userInfo.get("birthTime")).append("\n");
            }
            if (userInfo.get("birthPlace") != null) {
                prompt.append("  出生地：").append(userInfo.get("birthPlace")).append("\n");
            }
        }
        
        prompt.append("\n请根据以上信息，分析该用户在指定日期的能量运势，并按照要求的JSON格式返回结果。");
        
        return prompt.toString();
    }
    
    /**
     * 解析AI响应为能量数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseEnergyResponse(String responseText) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> aiJsonData = null;
            
            // 尝试直接解析JSON
            try {
                aiJsonData = mapper.readValue(responseText, Map.class);
                log.debug("直接JSON解析成功");
            } catch (Exception e) {
                log.warn("直接解析AI响应JSON失败: {}, 尝试提取JSON片段", e.getMessage());
                
                // 尝试从响应中提取JSON部分
                String jsonPart = extractJsonFromText(responseText);
                log.debug("提取的JSON部分: {}", jsonPart);
                
                if (jsonPart != null && !jsonPart.trim().isEmpty()) {
                    try {
                        aiJsonData = mapper.readValue(jsonPart, Map.class);
                        log.debug("提取JSON解析成功");
                    } catch (Exception ex) {
                        log.warn("提取的JSON解析失败: {}, JSON内容: {}", ex.getMessage(), jsonPart);
                    }
                }
            }
            
            // 如果成功解析了JSON，则进行字段映射转换
            if (aiJsonData != null && !aiJsonData.isEmpty()) {
                log.info("AI返回的原始JSON数据: {}", aiJsonData);
                return convertAiJsonToEnergyData(aiJsonData);
            }
            
            // 如果解析失败，返回基于文本分析的默认数据
            log.warn("所有JSON解析都失败，使用默认数据");
            return createDefaultEnergyDataFromText(responseText);
            
        } catch (Exception e) {
            log.error("解析AI响应过程中发生异常", e);
            return createDefaultEnergyDataFromText(responseText);
        }
    }
    
    /**
     * 将AI返回的JSON数据转换为系统期望的能量数据格式
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> convertAiJsonToEnergyData(Map<String, Object> aiJsonData) {
        Map<String, Object> energyData = new HashMap<>();
        
        log.debug("开始转换AI JSON数据: {}", aiJsonData.keySet());
        
        // 日期信息 - 从AI返回的dateInfo中获取或使用当前日期
        Map<String, Object> dateInfo = new HashMap<>();
        Object aiDateInfo = aiJsonData.get("dateInfo");
        if (aiDateInfo instanceof Map) {
            Map<String, Object> aiDateInfoMap = (Map<String, Object>) aiDateInfo;
            dateInfo.put("gregorian", aiDateInfoMap.getOrDefault("gregorian", java.time.LocalDate.now().toString()));
            dateInfo.put("lunar", aiDateInfoMap.getOrDefault("lunar", convertToLunarDate(java.time.LocalDate.now())));
        } else {
            String currentDate = java.time.LocalDate.now().toString();
            dateInfo.put("gregorian", currentDate);
            dateInfo.put("lunar", convertToLunarDate(java.time.LocalDate.now()));
        }
        energyData.put("dateInfo", dateInfo);
        
        // 1. 综合能量评分映射
        Integer totalScore = extractIntegerFromAi(aiJsonData, Arrays.asList("今日综合能量评分", "综合能量评分", "totalScore"), 75);
        energyData.put("totalScore", totalScore);
        energyData.put("percentage", Math.min(totalScore, 100));
        
        // 2. 五维能量分析映射 - 支持英文和中文字段名
        Object dimensionsObj = aiJsonData.get("dimensions");
        Object wuxingObj = aiJsonData.get("五维能量分析");
        
        if (dimensionsObj instanceof Map) {
            // AI返回了标准的dimensions结构
            energyData.put("dimensions", dimensionsObj);
        } else if (wuxingObj instanceof Map) {
            // 兼容中文字段名格式
            Map<String, Object> wuxingMap = (Map<String, Object>) wuxingObj;
            Map<String, Object> dimensions = new HashMap<>();
            dimensions.put("love", extractIntegerFromAi(wuxingMap, Arrays.asList("爱情", "love"), 75));
            dimensions.put("career", extractIntegerFromAi(wuxingMap, Arrays.asList("事业", "career"), 80));
            dimensions.put("wealth", extractIntegerFromAi(wuxingMap, Arrays.asList("财富", "wealth"), 70));
            dimensions.put("health", extractIntegerFromAi(wuxingMap, Arrays.asList("健康", "health"), 85));
            dimensions.put("relationship", extractIntegerFromAi(wuxingMap, Arrays.asList("人际关系", "relationship"), 75));
            energyData.put("dimensions", dimensions);
        } else {
            // 使用默认五维分析
            Map<String, Object> dimensions = new HashMap<>();
            dimensions.put("love", 75);
            dimensions.put("career", 80);
            dimensions.put("wealth", 70);
            dimensions.put("health", 85);
            dimensions.put("relationship", 75);
            energyData.put("dimensions", dimensions);
        }
        
        // 3. 能量高峰时段映射 - 支持英文和中文字段名
        String peakTime = "上午8-10点";
        String peakTimeDescription = "能量充沛的时段";
        
        // 尝试从不同的字段名获取peakTime
        Object peakTimeVal = aiJsonData.get("peakTime");
        if (peakTimeVal == null) {
            peakTimeVal = aiJsonData.get("能量高峰时段");
        }
        if (peakTimeVal == null) {
            Object peakTimeObj = aiJsonData.get("能量高峰时段和建议");
            if (peakTimeObj instanceof Map) {
                Map<String, Object> peakTimeMap = (Map<String, Object>) peakTimeObj;
                peakTimeVal = peakTimeMap.get("时段");
            }
        }
        if (peakTimeVal != null) {
            peakTime = peakTimeVal.toString();
        }
        
        // 尝试从不同的字段名获取peakTimeDescription
        Object peakTimeDescVal = aiJsonData.get("peakTimeDescription");
        if (peakTimeDescVal == null) {
            peakTimeDescVal = aiJsonData.get("能量高峰时段描述");
        }
        if (peakTimeDescVal == null) {
            Object peakTimeObj = aiJsonData.get("能量高峰时段和建议");
            if (peakTimeObj instanceof Map) {
                Map<String, Object> peakTimeMap = (Map<String, Object>) peakTimeObj;
                peakTimeDescVal = peakTimeMap.get("建议");
            }
        }
        if (peakTimeDescVal != null) {
            peakTimeDescription = peakTimeDescVal.toString();
        }
        
        energyData.put("peakTime", peakTime);
        energyData.put("peakTimeDescription", peakTimeDescription);
        
        // 4. 今日能量总体描述映射 - 支持英文和中文字段名
        String description = "今日能量平衡，整体运势良好";
        Object descVal = aiJsonData.get("description");
        if (descVal == null) {
            descVal = aiJsonData.get("今日能量总体描述");
        }
        if (descVal == null) {
            descVal = aiJsonData.get("总体描述");
        }
        if (descVal != null) {
            description = descVal.toString();
        }
        energyData.put("description", description);
        
        // 5. 宜忌事项映射 - 支持英文和中文字段名
        Map<String, Object> advice = new HashMap<>();
        List<Map<String, Object>> categories = new ArrayList<>();
        
        // 检查AI返回的advice结构
        Object adviceObj = aiJsonData.get("advice");
        if (adviceObj instanceof Map) {
            // AI返回了标准的advice结构
            Map<String, Object> adviceMap = (Map<String, Object>) adviceObj;
            Object categoriesObj = adviceMap.get("categories");
            if (categoriesObj instanceof List) {
                List<Map<String, Object>> categoriesList = (List<Map<String, Object>>) categoriesObj;
                for (Map<String, Object> categoryMap : categoriesList) {
                    String type = (String) categoryMap.get("type");
                    List<Map<String, Object>> items = (List<Map<String, Object>>) categoryMap.get("items");
                    if (items != null && !items.isEmpty()) {
                        Map<String, Object> convertedCategory = new HashMap<>();
                        convertedCategory.put("type", type);
                        convertedCategory.put("label", "suitable".equals(type) ? "宜做事项" : "忌做事项");
                        convertedCategory.put("items", items);
                        categories.add(convertedCategory);
                    }
                }
            }
        } else {
            // 兼容旧版中文字段名格式
            // 5.1 宜做事项
            Object suitableObj = aiJsonData.get("宜做事项");
            if (suitableObj instanceof List) {
                List<String> suitableList = (List<String>) suitableObj;
                Map<String, Object> suitableCategory = new HashMap<>();
                suitableCategory.put("type", "suitable");
                suitableCategory.put("label", "宜做事项");
                
                List<Map<String, Object>> suitableItems = new ArrayList<>();
                for (int i = 0; i < suitableList.size() && i < 4; i++) {
                    suitableItems.add(createAdviceItem("suitable" + (i + 1), "icon-V", suitableList.get(i)));
                }
                suitableCategory.put("items", suitableItems);
                categories.add(suitableCategory);
            }
            
            // 5.2 忌做事项
            Object avoidObj = aiJsonData.get("忌做事项");
            if (avoidObj instanceof List) {
                List<String> avoidList = (List<String>) avoidObj;
                Map<String, Object> avoidCategory = new HashMap<>();
                avoidCategory.put("type", "avoid");
                avoidCategory.put("label", "忌做事项");
                
                List<Map<String, Object>> avoidItems = new ArrayList<>();
                for (int i = 0; i < avoidList.size() && i < 4; i++) {
                    avoidItems.add(createAdviceItem("avoid" + (i + 1), "icon-V", avoidList.get(i)));
                }
                avoidCategory.put("items", avoidItems);
                categories.add(avoidCategory);
            }
        }
        
        advice.put("categories", categories);
        
        // 6. 生活建议映射 - 支持英文和中文字段名
        Object adviceLifeSuggestionsObj = null;
        if (adviceObj instanceof Map) {
            Map<String, Object> adviceMap = (Map<String, Object>) adviceObj;
            adviceLifeSuggestionsObj = adviceMap.get("lifeSuggestions");
        }
        
        if (adviceLifeSuggestionsObj instanceof List) {
            // AI返回了标准的lifeSuggestions结构
            advice.put("lifeSuggestions", adviceLifeSuggestionsObj);
        } else {
            // 兼容旧版中文字段名格式
            Object lifeSuggestionsObj = aiJsonData.get("具体生活建议");
            if (lifeSuggestionsObj instanceof List) {
                List<String> lifeSuggestionsList = (List<String>) lifeSuggestionsObj;
                List<Map<String, Object>> lifeSuggestions = new ArrayList<>();
                for (int i = 0; i < lifeSuggestionsList.size() && i < 4; i++) {
                    lifeSuggestions.add(createLifeSuggestion("icon-run", lifeSuggestionsList.get(i)));
                }
                advice.put("lifeSuggestions", lifeSuggestions);
            } else {
                // 默认生活建议
                List<Map<String, Object>> lifeSuggestions = Arrays.asList(
                    createLifeSuggestion("icon-run", "今日适合进行轻度运动，有助于提升整体能量状态"),
                    createLifeSuggestion("icon-aixuexi", "建议安排学习时间，专注力较佳的时段效果更好")
                );
                advice.put("lifeSuggestions", lifeSuggestions);
            }
        }
        
        energyData.put("advice", advice);
        
        // 7. 幸运元素映射 - 支持英文和中文字段名
        Object standardLuckyElementsObj = aiJsonData.get("luckyElements");
        Object chineseLuckyElementsObj = aiJsonData.get("今日幸运元素");
        
        if (standardLuckyElementsObj instanceof Map) {
            // AI返回了标准的luckyElements结构，直接使用
            energyData.put("luckyElements", standardLuckyElementsObj);
        } else if (chineseLuckyElementsObj instanceof Map) {
            // 兼容中文字段名格式
            Map<String, Object> luckyElementsMap = (Map<String, Object>) chineseLuckyElementsObj;
            Map<String, Object> luckyElements = new HashMap<>();
            
            // 颜色
            String colorsStr = (String) luckyElementsMap.get("颜色");
            if (colorsStr != null) {
                luckyElements.put("colors", Arrays.asList(colorsStr.split("、")));
            } else {
                luckyElements.put("colors", Arrays.asList("#4ECDC4", "#FF6B6B"));
            }
            
            // 服饰
            String clothingStr = (String) luckyElementsMap.get("服饰");
            if (clothingStr != null) {
                luckyElements.put("clothing", Arrays.asList(clothingStr));
            } else {
                luckyElements.put("clothing", Arrays.asList("推荐穿着舒适的服装"));
            }
            
            // 配饰
            String accessoriesStr = (String) luckyElementsMap.get("配饰");
            if (accessoriesStr != null) {
                luckyElements.put("accessories", Arrays.asList(accessoriesStr));
            } else {
                luckyElements.put("accessories", Arrays.asList("简约的配饰"));
            }
            
            // 妆容
            String makeupStr = (String) luckyElementsMap.get("妆容");
            if (makeupStr != null) {
                luckyElements.put("makeup", Arrays.asList(makeupStr));
            } else {
                luckyElements.put("makeup", Arrays.asList("清淡自然的妆容"));
            }
            
            energyData.put("luckyElements", luckyElements);
        } else {
            // 默认幸运元素
            Map<String, Object> luckyElements = new HashMap<>();
            // 默认3个颜色
            List<Map<String, Object>> defaultColors = Arrays.asList(
                createColorItem("#4ECDC4", "薄荷绿"),
                createColorItem("#FF6B6B", "珊瑚红"),
                createColorItem("#FFD93D", "金黄色")
            );
            luckyElements.put("colors", defaultColors);
            luckyElements.put("clothing", Arrays.asList("推荐穿着舒适的服装"));
            luckyElements.put("accessories", Arrays.asList("简约的配饰"));
            luckyElements.put("makeup", Arrays.asList("清淡自然的妆容"));
            energyData.put("luckyElements", luckyElements);
        }
        
        log.debug("AI JSON转换完成，综合评分: {}", totalScore);
        return energyData;
    }
    
    /**
     * 从AI数据中提取整数值的辅助方法
     */
    private Integer extractIntegerFromAi(Map<String, Object> data, List<String> possibleKeys, Integer defaultValue) {
        for (String key : possibleKeys) {
            Object value = data.get(key);
            if (value != null) {
                if (value instanceof Number) {
                    return ((Number) value).intValue();
                } else if (value instanceof String) {
                    try {
                        return Integer.parseInt((String) value);
                    } catch (NumberFormatException e) {
                        // 继续尝试下一个key
                    }
                }
            }
        }
        return defaultValue;
    }
    
    /**
     * 从文本中提取JSON部分
     */
    private String extractJsonFromText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        
        // 移除可能的markdown代码块标记
        String cleanedText = text.trim();
        if (cleanedText.startsWith("```json")) {
            cleanedText = cleanedText.substring(7);
        } else if (cleanedText.startsWith("```")) {
            cleanedText = cleanedText.substring(3);
        }
        if (cleanedText.endsWith("```")) {
            cleanedText = cleanedText.substring(0, cleanedText.length() - 3);
        }
        
        cleanedText = cleanedText.trim();
        
        // 找到第一个{和最后一个}
        int startIndex = cleanedText.indexOf("{");
        int endIndex = cleanedText.lastIndexOf("}");
        
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return cleanedText.substring(startIndex, endIndex + 1);
        }
        
        return null;
    }
    
    /**
     * 创建默认能量数据
     */
    private Map<String, Object> createDefaultEnergyData(Map<String, Object> request) {
        Map<String, Object> energyData = new HashMap<>();
        
        // 日期信息
        Map<String, Object> dateInfo = new HashMap<>();
        String dateStr = (String) request.get("date");
        dateInfo.put("gregorian", dateStr);
        
        // 转换农历日期
        try {
            java.time.LocalDate date = java.time.LocalDate.parse(dateStr);
            dateInfo.put("lunar", convertToLunarDate(date));
        } catch (Exception e) {
            dateInfo.put("lunar", convertToLunarDate(java.time.LocalDate.now()));
        }
        energyData.put("dateInfo", dateInfo);
        
        // 基本能量数据
        energyData.put("totalScore", 75);
        energyData.put("percentage", 60);
        energyData.put("peakTime", "上午8-10点");
        energyData.put("peakTimeDescription", "适合重要决策和创意工作");
        energyData.put("description", "今日能量平衡，整体运势平稳");
        
        // 五维能量评分
        Map<String, Object> dimensions = new HashMap<>();
        dimensions.put("love", 75);
        dimensions.put("career", 80);
        dimensions.put("wealth", 70);
        dimensions.put("health", 85);
        dimensions.put("relationship", 75);
        energyData.put("dimensions", dimensions);
        
        // 宜忌指南
        Map<String, Object> advice = new HashMap<>();
        
        // 宜做事项 - 4条
        Map<String, Object> suitableCategory = new HashMap<>();
        suitableCategory.put("type", "suitable");
        suitableCategory.put("label", "宜做事项");
        List<Map<String, Object>> suitableItems = Arrays.asList(
            createAdviceItem("suitable1", "icon-V", "理性投资"),
            createAdviceItem("suitable2", "icon-fangkeguanli", "真诚沟通"),
            createAdviceItem("suitable3", "icon-run", "适度运动"),
            createAdviceItem("suitable4", "icon-aixuexi", "专心学习")
        );
        suitableCategory.put("items", suitableItems);
        
        // 忌做事项 - 4条  
        Map<String, Object> avoidCategory = new HashMap<>();
        avoidCategory.put("type", "avoid");
        avoidCategory.put("label", "忌做事项");
        List<Map<String, Object>> avoidItems = Arrays.asList(
            createAdviceItem("avoid1", "icon-V", "冲动消费"),
            createAdviceItem("avoid2", "icon-fangkeguanli", "激烈争执"),
            createAdviceItem("avoid3", "icon-shixichengguo", "过度娱乐"),
            createAdviceItem("avoid4", "icon-xinlizixun", "情绪焦躁")
        );
        avoidCategory.put("items", avoidItems);
        
        advice.put("categories", Arrays.asList(suitableCategory, avoidCategory));
        
        // 生活建议 - 2条：宜和忌
        List<Map<String, Object>> lifeSuggestions = Arrays.asList(
            createLifeSuggestion("icon-run", "今日能量稳定，适合进行轻度运动和思考规划，保持积极乐观的心态有助于运势提升"),
            createLifeSuggestion("icon-xinlizixun", "避免过度焦虑和冲动决策，不适合进行重大投资或情感表白，容易遇到阻碍")
        );
        advice.put("lifeSuggestions", lifeSuggestions);
        energyData.put("advice", advice);
        
        // 幸运元素
        Map<String, Object> luckyElements = new HashMap<>();
        // 默认3个颜色
        List<Map<String, Object>> defaultColors = Arrays.asList(
            createColorItem("#4ECDC4", "薄荷绿"),
            createColorItem("#FF6B6B", "珊瑚红"),
            createColorItem("#FFD93D", "金黄色")
        );
        luckyElements.put("colors", defaultColors);
        luckyElements.put("clothing", Arrays.asList("推荐穿着舒适的服装"));
        luckyElements.put("accessories", Arrays.asList("简约的配饰"));
        luckyElements.put("makeup", Arrays.asList("清淡自然的妆容"));
        energyData.put("luckyElements", luckyElements);
        
        return energyData;
    }
    
    /**
     * 基于AI文本响应创建能量数据
     */
    private Map<String, Object> createDefaultEnergyDataFromText(String responseText) {
        // 这里可以根据AI的文本响应提取关键信息
        // 暂时返回默认数据
        Map<String, Object> request = new HashMap<>();
        request.put("date", java.time.LocalDate.now().toString());
        return createDefaultEnergyData(request);
    }
    
    /**
     * 创建宜忌建议项
     */
    private Map<String, Object> createAdviceItem(String id, String icon, String text) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", id);
        item.put("icon", icon);
        item.put("text", text);
        return item;
    }
    
    /**
     * 创建生活建议项
     */
    private Map<String, Object> createLifeSuggestion(String icon, String content) {
        Map<String, Object> suggestion = new HashMap<>();
        suggestion.put("icon", icon);
        suggestion.put("content", content);
        return suggestion;
    }
    
    /**
     * 创建颜色项
     */
    private Map<String, Object> createColorItem(String value, String name) {
        Map<String, Object> colorItem = new HashMap<>();
        colorItem.put("value", value);
        colorItem.put("name", name);
        return colorItem;
    }
    
    /**
     * 将公历日期转换为农历日期描述
     * 简化版本，使用基础的农历年号和月日描述
     */
    private String convertToLunarDate(java.time.LocalDate gregorianDate) {
        try {
            // 简化的农历转换逻辑
            int year = gregorianDate.getYear();
            int month = gregorianDate.getMonthValue();
            int day = gregorianDate.getDayOfMonth();
            
            // 天干地支年份转换（简化版）
            String[] tianGan = {"甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"};
            String[] diZhi = {"子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"};
            String[] zodiac = {"鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"};
            
            // 计算农历年份（简化计算，实际应该更复杂）
            int lunarYearIndex = (year - 1984) % 60; // 1984年为甲子年
            String lunarYear = tianGan[lunarYearIndex % 10] + diZhi[lunarYearIndex % 12];
            String zodiacAnimal = zodiac[lunarYearIndex % 12];
            
            // 简化的月份转换（实际农历月份需要复杂计算）
            String[] chineseMonths = {"正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"};
            String lunarMonth = chineseMonths[(month - 1) % 12] + "月";
            
            // 农历日期（简化处理）
            String[] chineseDays = {"初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
                                   "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
                                   "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十"};
            String lunarDay = chineseDays[(day - 1) % 30];
            
            return String.format("%s年（%s年）%s%s", lunarYear, zodiacAnimal, lunarMonth, lunarDay);
            
        } catch (Exception e) {
            log.warn("农历转换失败: {}", e.getMessage());
            return "农历信息";
        }
    }

    @Override
    public Map<String, Object> generateRecommendation(Map<String, Object> request) {
        try {
            log.info("DeepSeek开始生成穿搭推荐，用户: {}", request.get("userId"));

            // 构建推荐系统提示词
            String systemPrompt = buildRecommendationSystemPrompt();

            // 构建用户请求提示词
            String userPrompt = buildRecommendationUserPrompt(request);

            // 调用DeepSeek生成推荐
            ChatResponse response = chatClient.prompt()
                .system(systemPrompt)
                .user(userPrompt)
                .options(DeepSeekChatOptions.builder()
                    .model(DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue())
                    .temperature(0.7)  // 适中的创意度
                    .maxTokens(8000)   // 足够的token支持详细推荐
                    .build())
                .call()
                .chatResponse();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("aiResponse", response.getResult().getOutput().getText());
            result.put("model", DeepSeekApi.ChatModel.DEEPSEEK_CHAT.getValue());

            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                result.put("tokensUsed", response.getMetadata().getUsage().getTotalTokens());
            }

            log.info("DeepSeek穿搭推荐生成完成，回复长度: {}",
                response.getResult().getOutput().getText().length());

            return result;

        } catch (Exception e) {
            log.error("DeepSeek穿搭推荐生成失败", e);
            throw new RuntimeException("穿搭推荐生成失败: " + e.getMessage());
        }
    }

    /**
     * 构建推荐系统提示词
     */
    private String buildRecommendationSystemPrompt() {
        return "你是一位专业的时尚搭配顾问和五行命理专家，拥有丰富的服装搭配经验和深厚的中华传统文化底蕴。" +
            "你需要根据用户的个人信息、天气情况、五行命理和使用场景，为用户生成3套完整的个性化穿搭推荐。" +
            "\n\n每套穿搭必须包含以下完整元素：" +
            "\n- 上装（衬衫、T恤、毛衣、外套等）" +
            "\n- 下装（裤子、裙子等）" +
            "\n- 鞋子（皮鞋、运动鞋、高跟鞋、靴子等）" +
            "\n- 配饰（包包、腰带、项链、手表、帽子、围巾等，至少2-3样）" +
            "\n- 如需要可包含内搭、袜子等其他单品" +
            "\n\n请严格按照以下JSON格式返回结果，确保JSON格式正确且完整：\n" +
            "{\n" +
            "  \"outfits\": [\n" +
            "    {\n" +
            "      \"outfitName\": \"搭配名称（如：优雅商务套装）\",\n" +
            "      \"description\": \"整体搭配描述\",\n" +
            "      \"items\": [\n" +
            "        {\n" +
            "          \"itemName\": \"白色长袖衬衫\",\n" +
            "          \"itemType\": \"上装\",\n" +
            "          \"color\": \"白色\",\n" +
            "          \"material\": \"棉质\",\n" +
            "          \"wuxingElement\": \"金\",\n" +
            "          \"itemDescription\": \"白色纯棉长袖衬衫商务款\",\n" +
            "          \"tags\": [\"商务\", \"基础款\", \"百搭\"],\n" +
            "          \"score\": 85\n" +
            "        },\n" +
            "        {\n" +
            "          \"itemName\": \"深蓝色西装裤\",\n" +
            "          \"itemType\": \"下装\",\n" +
            "          \"color\": \"深蓝色\",\n" +
            "          \"material\": \"羊毛混纺\",\n" +
            "          \"wuxingElement\": \"水\",\n" +
            "          \"itemDescription\": \"深蓝色修身西装裤\",\n" +
            "          \"tags\": [\"商务\", \"修身\", \"正式\"],\n" +
            "          \"score\": 88\n" +
            "        },\n" +
            "        {\n" +
            "          \"itemName\": \"黑色皮鞋\",\n" +
            "          \"itemType\": \"鞋子\",\n" +
            "          \"color\": \"黑色\",\n" +
            "          \"material\": \"真皮\",\n" +
            "          \"wuxingElement\": \"水\",\n" +
            "          \"itemDescription\": \"黑色商务皮鞋\",\n" +
            "          \"tags\": [\"商务\", \"正式\", \"经典\"],\n" +
            "          \"score\": 90\n" +
            "        },\n" +
            "        {\n" +
            "          \"itemName\": \"棕色真皮公文包\",\n" +
            "          \"itemType\": \"包包\",\n" +
            "          \"color\": \"棕色\",\n" +
            "          \"material\": \"真皮\",\n" +
            "          \"wuxingElement\": \"土\",\n" +
            "          \"itemDescription\": \"棕色商务公文包\",\n" +
            "          \"tags\": [\"商务\", \"实用\", \"经典\"],\n" +
            "          \"score\": 82\n" +
            "        },\n" +
            "        {\n" +
            "          \"itemName\": \"银色商务手表\",\n" +
            "          \"itemType\": \"配饰\",\n" +
            "          \"color\": \"银色\",\n" +
            "          \"material\": \"不锈钢\",\n" +
            "          \"wuxingElement\": \"金\",\n" +
            "          \"itemDescription\": \"银色不锈钢商务手表\",\n" +
            "          \"tags\": [\"商务\", \"精致\", \"时尚\"],\n" +
            "          \"score\": 87\n" +
            "        }\n" +
            "      ],\n" +
            "      \"score\": 4.5,\n" +
            "      \"suitableOccasions\": [\"商务会议\", \"正式场合\", \"工作日\"]\n" +
            "    }\n" +
            "  ],\n" +
            "  \"reason\": \"基于用户偏好、天气和五行分析的推荐理由\",\n" +
            "  \"wuxingAnalysis\": {\n" +
            "    \"metal\": 40,\n" +
            "    \"wood\": 10,\n" +
            "    \"water\": 30,\n" +
            "    \"fire\": 5,\n" +
            "    \"earth\": 15,\n" +
            "    \"summary\": \"此搭配以金水为主，稳重大方\",\n" +
            "    \"details\": [\n" +
            "      {\n" +
            "        \"element\": \"金\",\n" +
            "        \"value\": 40,\n" +
            "        \"summary\": \"白色衬衫和银色手表体现金元素\"\n" +
            "      }\n" +
            "    ]\n" +
            "  },\n" +
            "  \"energyAdvice\": [\n" +
            "    {\n" +
            "      \"element\": \"木\",\n" +
            "      \"advice\": \"可搭配绿色小饰品增添活力\"\n" +
            "    }\n" +
            "  ],\n" +
            "  \"totalScore\": 4.5,\n" +
            "  \"multiScore\": {\n" +
            "    \"energy\": 4.5,\n" +
            "    \"occasion\": 4.2,\n" +
            "    \"style\": 4.8,\n" +
            "    \"temperament\": 4.3\n" +
            "  }\n" +
            "}\n" +
            "\n重要要求：\n" +
            "1. 必须生成3套完整搭配，每套至少包含：上装、下装、鞋子、2-3样配饰\n" +
            "2. 每个单品都要有详细的描述和五行属性\n" +
            "3. 考虑天气、场合、用户偏好和五行相生相克原理\n" +
            "4. 搭配要实用且时尚，避免过于夸张或不实际的组合\n" +
            "5. 配饰要多样化：包包、腰带、项链、手表、帽子、围巾等";
    }

    /**
     * 构建推荐用户提示词
     */
    private String buildRecommendationUserPrompt(Map<String, Object> request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请为以下用户生成穿搭推荐：\n\n");

        // 用户基本信息
        prompt.append("用户ID: ").append(request.get("userId")).append("\n");
        prompt.append("推荐日期: ").append(request.get("date")).append("\n");

        // 使用场景
        if (request.get("occasions") != null) {
            prompt.append("使用场景: ").append(request.get("occasions")).append("\n");
        }

        // 天气信息
        if (request.get("weatherInfo") != null) {
            prompt.append("天气信息: ").append(request.get("weatherInfo")).append("\n");
        }

        // 五行信息
        if (request.get("wuxingInfo") != null) {
            prompt.append("五行信息: ").append(request.get("wuxingInfo")).append("\n");
        }

        // 用户偏好
        if (request.get("userPreferences") != null) {
            prompt.append("用户偏好: ").append(request.get("userPreferences")).append("\n");
        }

        prompt.append("\n请为该用户生成3套完整的专业穿搭推荐：\n");
        prompt.append("1. 每套搭配必须包含：上装、下装、鞋子、配饰（至少2-3样）\n");
        prompt.append("2. 考虑天气条件选择合适的材质和厚度\n");
        prompt.append("3. 根据使用场合选择适当的正式程度\n");
        prompt.append("4. 结合五行相生相克原理，确保颜色和元素和谐\n");
        prompt.append("5. 考虑用户的身材特点和个人偏好\n");
        prompt.append("6. 配饰要实用且时尚，包括包包、腰带、首饰、手表等\n");
        prompt.append("7. 每套搭配要有不同的风格定位（如商务、休闲、优雅等）");

        return prompt.toString();
    }
}