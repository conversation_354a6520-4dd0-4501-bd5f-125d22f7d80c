package com.stylishlink.ai.service.impl;

import com.stylishlink.ai.client.AiClient;
import com.stylishlink.ai.client.AiClientFactory;
import com.stylishlink.ai.service.AiRecommendationService;
import com.stylishlink.ai.dto.request.AiRecommendationRequest;
import com.stylishlink.ai.dto.response.AiRecommendationResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class AiRecommendationServiceImpl implements AiRecommendationService {

    @Autowired
    private AiClientFactory aiClientFactory;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public AiRecommendationResponse generateDailyRecommendation(AiRecommendationRequest request) {
        try {
            // 获取AI客户端
            AiClient aiClient = aiClientFactory.getDefaultClient();
            
            // 构建AI请求参数
            Map<String, Object> params = buildRecommendationParams(request);
            
            // 调用AI服务生成推荐
            Map<String, Object> result = aiClient.generateRecommendation(params);
            
            // 转换为响应对象
            return convertToResponse(result);
        } catch (Exception e) {
            log.error("生成每日穿搭推荐失败", e);
            return createFallbackResponse(request);
        }
    }
    
    // 构建AI请求参数
    private Map<String, Object> buildRecommendationParams(AiRecommendationRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", request.getUserId());
        params.put("date", request.getDate());
        params.put("occasions", request.getOccasions());
        params.put("weatherInfo", request.getWeatherInfo());
        params.put("wuxingInfo", request.getWuxingInfo());
        params.put("userPreferences", request.getUserPreferences());
        return params;
    }
    
    // 转换AI响应为DTO对象
    private AiRecommendationResponse convertToResponse(Map<String, Object> result) {
        try {
            log.info("开始转换AI响应，result keys: {}", result.keySet());

            // 获取AI返回的JSON字符串
            String aiResponse = (String) result.get("aiResponse");
            if (aiResponse == null || aiResponse.trim().isEmpty()) {
                log.error("AI响应为空，result: {}", result);
                throw new RuntimeException("AI服务返回空响应");
            }

            log.info("AI响应内容长度: {}, 前500字符: {}",
                aiResponse.length(),
                aiResponse.length() > 500 ? aiResponse.substring(0, 500) + "..." : aiResponse);

            // 清理markdown代码块标记
            String cleanedResponse = cleanMarkdownCodeBlocks(aiResponse);
            log.info("清理后的JSON长度: {}, 前200字符: {}",
                cleanedResponse.length(),
                cleanedResponse.length() > 200 ? cleanedResponse.substring(0, 200) + "..." : cleanedResponse);

            // 解析JSON
            JsonNode jsonNode;
            try {
                jsonNode = objectMapper.readTree(cleanedResponse);
                log.info("JSON解析成功，根节点字段: {}",
                    jsonNode.isObject() ? jsonNode.fieldNames() : "非对象类型");
            } catch (Exception jsonException) {
                log.error("JSON解析失败，清理后的响应: {}", cleanedResponse, jsonException);
                throw new RuntimeException("AI响应JSON格式错误: " + jsonException.getMessage(), jsonException);
            }

            // 构建响应对象
            AiRecommendationResponse response = AiRecommendationResponse.builder()
                .outfits(parseOutfits(jsonNode))
                .reason(jsonNode.path("reason").asText("基于您的偏好和当日五行分析生成的推荐"))
                .wuxingAnalysis(parseWuxingAnalysis(jsonNode))
                .energyAdvice(parseEnergyAdvice(jsonNode))
                .totalScore(jsonNode.path("totalScore").asDouble(4.5))
                .multiScore(parseMultiScore(jsonNode))
                .build();

            log.info("AI响应转换成功，outfits数量: {}, reason: {}",
                response.getOutfits() != null ? response.getOutfits().size() : 0,
                response.getReason());

            return response;

        } catch (Exception e) {
            log.error("解析AI响应失败，result: {}", result, e);
            throw new RuntimeException("AI推荐结果解析失败: " + e.getMessage(), e);
        }
    }
    
    // 解析推荐搭配
    private List<AiRecommendationResponse.OutfitRecommendation> parseOutfits(JsonNode jsonNode) {
        List<AiRecommendationResponse.OutfitRecommendation> outfits = new ArrayList<>();

        JsonNode outfitsNode = jsonNode.path("outfits");
        log.info("解析outfits，节点类型: {}, 是否为数组: {}",
            outfitsNode.getNodeType(), outfitsNode.isArray());

        if (outfitsNode.isArray()) {
            log.info("outfits数组长度: {}", outfitsNode.size());
            for (int i = 0; i < outfitsNode.size(); i++) {
                JsonNode outfitNode = outfitsNode.get(i);
                try {
                    log.info("解析第{}套搭配，outfitName: {}", i + 1,
                        outfitNode.path("outfitName").asText("未知"));

                    AiRecommendationResponse.OutfitRecommendation outfit =
                        AiRecommendationResponse.OutfitRecommendation.builder()
                            .outfitName(outfitNode.path("outfitName").asText())
                            .description(outfitNode.path("description").asText())
                            .items(parseClothingItems(outfitNode.path("items")))
                            .score(outfitNode.path("score").asDouble(4.0))
                            .suitableOccasions(parseStringArray(outfitNode.path("suitableOccasions")))
                            .build();
                    outfits.add(outfit);
                    log.info("第{}套搭配解析成功，单品数量: {}", i + 1,
                        outfit.getItems() != null ? outfit.getItems().size() : 0);
                } catch (Exception e) {
                    log.error("解析第{}套搭配失败，节点内容: {}", i + 1, outfitNode, e);
                }
            }
        } else {
            log.warn("outfits节点不是数组类型，节点内容: {}", outfitsNode);
        }

        log.info("总共解析到{}套搭配", outfits.size());
        return outfits;
    }

    // 解析单品列表
    private List<AiRecommendationResponse.ClothingItem> parseClothingItems(JsonNode itemsNode) {
        List<AiRecommendationResponse.ClothingItem> items = new ArrayList<>();

        log.info("解析单品列表，节点类型: {}, 是否为数组: {}",
            itemsNode.getNodeType(), itemsNode.isArray());

        if (itemsNode.isArray()) {
            log.info("单品数组长度: {}", itemsNode.size());
            for (int i = 0; i < itemsNode.size(); i++) {
                JsonNode itemNode = itemsNode.get(i);
                try {
                    String itemName = itemNode.path("itemName").asText();
                    String itemType = itemNode.path("itemType").asText();
                    log.info("解析第{}个单品: {} ({})", i + 1, itemName, itemType);

                    AiRecommendationResponse.ClothingItem item =
                        AiRecommendationResponse.ClothingItem.builder()
                            .itemId(itemNode.path("itemId").asText(""))
                            .itemName(itemName)
                            .itemType(itemType)
                            .color(itemNode.path("color").asText())
                            .material(itemNode.path("material").asText())
                            .wuxingElement(itemNode.path("wuxingElement").asText())
                            .itemDescription(itemNode.path("itemDescription").asText())
                            .tags(parseStringArray(itemNode.path("tags")))
                            .score(itemNode.path("score").asInt(80))
                            .build();
                    items.add(item);
                    log.info("第{}个单品解析成功", i + 1);
                } catch (Exception e) {
                    log.error("解析第{}个单品失败，节点内容: {}", i + 1, itemNode, e);
                }
            }
        } else {
            log.warn("单品节点不是数组类型，节点内容: {}", itemsNode);
        }

        log.info("总共解析到{}个单品", items.size());
        return items;
    }
    
    // 解析五行分析
    private AiRecommendationResponse.WuxingAnalysis parseWuxingAnalysis(JsonNode jsonNode) {
        JsonNode wuxingNode = jsonNode.path("wuxingAnalysis");
        if (wuxingNode.isMissingNode()) {
            return null;
        }

        try {
            return AiRecommendationResponse.WuxingAnalysis.builder()
                .metal(wuxingNode.path("metal").asInt(0))
                .wood(wuxingNode.path("wood").asInt(0))
                .water(wuxingNode.path("water").asInt(0))
                .fire(wuxingNode.path("fire").asInt(0))
                .earth(wuxingNode.path("earth").asInt(0))
                .summary(wuxingNode.path("summary").asText(""))
                .details(parseWuxingDetails(wuxingNode.path("details")))
                .build();
        } catch (Exception e) {
            log.warn("解析五行分析失败", e);
            return null;
        }
    }

    // 解析五行详情
    private List<AiRecommendationResponse.WuxingDetail> parseWuxingDetails(JsonNode detailsNode) {
        List<AiRecommendationResponse.WuxingDetail> details = new ArrayList<>();

        if (detailsNode.isArray()) {
            for (JsonNode detailNode : detailsNode) {
                try {
                    AiRecommendationResponse.WuxingDetail detail =
                        AiRecommendationResponse.WuxingDetail.builder()
                            .element(detailNode.path("element").asText())
                            .value(detailNode.path("value").asInt(0))
                            .summary(detailNode.path("summary").asText())
                            .build();
                    details.add(detail);
                } catch (Exception e) {
                    log.warn("解析五行详情失败", e);
                }
            }
        }

        return details;
    }
    
    // 解析能量建议
    private List<AiRecommendationResponse.EnergyAdvice> parseEnergyAdvice(JsonNode jsonNode) {
        List<AiRecommendationResponse.EnergyAdvice> adviceList = new ArrayList<>();

        JsonNode adviceNode = jsonNode.path("energyAdvice");
        if (adviceNode.isArray()) {
            for (JsonNode advice : adviceNode) {
                try {
                    AiRecommendationResponse.EnergyAdvice energyAdvice =
                        AiRecommendationResponse.EnergyAdvice.builder()
                            .element(advice.path("element").asText())
                            .advice(advice.path("advice").asText())
                            .build();
                    adviceList.add(energyAdvice);
                } catch (Exception e) {
                    log.warn("解析能量建议失败", e);
                }
            }
        }

        return adviceList;
    }
    
    // 解析多维度评分
    private Map<String, Double> parseMultiScore(JsonNode jsonNode) {
        Map<String, Double> scores = new HashMap<>();

        JsonNode multiScoreNode = jsonNode.path("multiScore");
        if (!multiScoreNode.isMissingNode()) {
            try {
                scores.put("energy", multiScoreNode.path("energy").asDouble(4.0));
                scores.put("occasion", multiScoreNode.path("occasion").asDouble(4.0));
                scores.put("style", multiScoreNode.path("style").asDouble(4.0));
                scores.put("temperament", multiScoreNode.path("temperament").asDouble(4.0));
            } catch (Exception e) {
                log.warn("解析多维度评分失败", e);
            }
        }

        // 如果解析失败，返回默认值
        if (scores.isEmpty()) {
            scores.put("energy", 4.0);
            scores.put("occasion", 4.0);
            scores.put("style", 4.0);
            scores.put("temperament", 4.0);
        }

        return scores;
    }

    // 解析字符串数组
    private List<String> parseStringArray(JsonNode arrayNode) {
        List<String> result = new ArrayList<>();

        if (arrayNode.isArray()) {
            for (JsonNode item : arrayNode) {
                result.add(item.asText());
            }
        }

        return result;
    }
    
    // 创建降级响应
    private AiRecommendationResponse createFallbackResponse(AiRecommendationRequest request) {
        log.warn("AI服务调用失败，返回降级响应");
        throw new RuntimeException("AI推荐服务暂时不可用，请稍后重试");
    }

    // 创建基础降级响应（用于JSON解析失败）
    private AiRecommendationResponse createBasicFallbackResponse() {
        log.warn("AI响应解析失败，返回基础降级响应");
        throw new RuntimeException("AI推荐结果解析失败，请稍后重试");
    }

    /**
     * 清理markdown代码块标记
     */
    private String cleanMarkdownCodeBlocks(String response) {
        if (response == null || response.trim().isEmpty()) {
            return response;
        }

        String cleaned = response.trim();

        // 移除开头的markdown代码块标记
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7).trim();
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3).trim();
        }

        // 移除结尾的markdown代码块标记
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3).trim();
        }

        return cleaned;
    }
}