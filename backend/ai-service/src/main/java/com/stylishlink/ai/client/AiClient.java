package com.stylishlink.ai.client;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * AI服务客户端接口
 */
public interface AiClient {

    /**
     * 图像识别
     * @param imageFile 图片文件
     * @param recognitionType 识别类型
     * @return 识别结果
     */
    Map<String, Object> recognizeImage(MultipartFile imageFile, String recognitionType);

    /**
     * 图像识别（通过URL方式）
     * @param imageUrl 图片URL
     * @param recognitionType 识别类型
     * @return 识别结果
     */
    Map<String, Object> recognizeImageByUrl(String imageUrl, String recognitionType);

    /**
     * 风格分析
     * @param imageUrl 图片URL
     * @return 风格分析结果
     */
    Map<String, Object> analyzeStyle(String imageUrl);

    /**
     * 色彩分析
     * @param imageUrl 图片URL
     * @return 色彩分析结果
     */
    Map<String, Object> analyzeColor(String imageUrl);

    /**
     * AI对话
     * @param message 用户消息
     * @param context 上下文
     * @return AI回复
     */
    Map<String, Object> chat(String message, String context);

    /**
     * 形象评估
     * @param imageUrls 图片URL列表
     * @param evaluationType 评估类型
     * @return 评估结果
     */
    Map<String, Object> evaluateAppearance(String[] imageUrls, String evaluationType);

    /**
     * 穿搭评分
     * @param outfitData 搭配数据
     * @return 评分结果
     */
    Map<String, Object> rateOutfit(Map<String, Object> outfitData);

    /**
     * 身材识别（文件上传方式）
     * @param imageFile 图片文件
     * @return 身材识别结果
     */
    Map<String, Object> analyzeBodyShape(MultipartFile imageFile);

    /**
     * 身材识别（图片URL方式）
     * @param imageUrl 图片URL地址
     * @return 身材识别结果
     */
    Map<String, Object> analyzeBodyShapeByUrl(String imageUrl);

    /**
     * 计算能量信息
     * @param request 能量计算请求数据
     * @return 能量计算结果
     */
    Map<String, Object> calculateEnergyInfo(Map<String, Object> request);

    /**
     * 生成穿搭推荐
     * @param request 推荐请求数据
     * @return 推荐结果
     */
    Map<String, Object> generateRecommendation(Map<String, Object> request);
}