package com.stylishlink.ai.controller;

import com.stylishlink.ai.service.AiRecommendationService;
import com.stylishlink.common.dto.ApiResponse;
import com.stylishlink.ai.dto.request.AiRecommendationRequest;
import com.stylishlink.ai.dto.response.AiRecommendationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/ai/recommendation")
public class AiRecommendationController {

    @Autowired
    private AiRecommendationService aiRecommendationService;

    @PostMapping("/daily")
    public ApiResponse<AiRecommendationResponse> generateDailyRecommendation(@Valid @RequestBody AiRecommendationRequest request) {
        log.info("收到每日穿搭推荐请求: {}", request);
        try {
            AiRecommendationResponse response = aiRecommendationService.generateDailyRecommendation(request);
            return ApiResponse.success("生成每日穿搭推荐成功", response);
        } catch (Exception e) {
            log.error("生成每日穿搭推荐失败", e);
            return ApiResponse.error(6002, "生成每日穿搭推荐失败: " + e.getMessage());
        }
    }
}