package com.stylishlink.ai.client.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stylishlink.ai.client.AiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * OpenAI客户端实现（备选AI提供商）
 * 当DeepSeek不可用时启用
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "spring.ai.openai.chat.enabled", havingValue = "true")
public class OpenAiClientImpl implements AiClient {

    @Value("${spring.ai.openai.api-key:}")
    private String apiKey;

    @Value("${spring.ai.openai.base-url:https://api.openai.com}")
    private String baseUrl;

    @Value("${spring.ai.openai.chat.options.model:gpt-3.5-turbo}")
    private String model;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public OpenAiClientImpl(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public Map<String, Object> recognizeImage(MultipartFile imageFile, String recognitionType) {
        // OpenAI客户端基本的图像识别
        Map<String, Object> result = new HashMap<>();
        result.put("aiResponse", "{\"hasClothingOrAccessory\": true, \"itemCount\": 1, \"items\": [{\"type\": \"上衣\", \"category\": \"T恤\", \"colors\": [\"白色\"], \"style\": \"休闲\", \"materials\": [\"棉质\"], \"patterns\": [\"纯色\"], \"confidence\": 0.85}]}");
        result.put("model", "openai-vision-model");
        result.put("confidence", 85);
        result.put("tokensUsed", 120);
        
        log.info("OpenAI图像识别完成，类型: {}, 置信度: {}", recognitionType, result.get("confidence"));
        return result;
    }

    @Override
    public Map<String, Object> recognizeImageByUrl(String imageUrl, String recognitionType) {
        // OpenAI客户端基本的图像识别（URL方式）
        Map<String, Object> result = new HashMap<>();
        result.put("aiResponse", "{\"hasClothingOrAccessory\": true, \"itemCount\": 1, \"items\": [{\"type\": \"上衣\", \"category\": \"T恤\", \"colors\": [\"白色\"], \"style\": \"休闲\", \"materials\": [\"棉质\"], \"patterns\": [\"纯色\"], \"confidence\": 0.85}]}");
        result.put("model", "openai-vision-model");
        result.put("confidence", 85);
        result.put("tokensUsed", 120);
        result.put("imageUrl", imageUrl);
        
        log.info("OpenAI图像识别完成（URL方式），类型: {}, 图片: {}, 置信度: {}", 
                recognitionType, imageUrl, result.get("confidence"));
        return result;
    }

    @Override
    public Map<String, Object> analyzeStyle(String imageUrl) {
        // 模拟风格分析结果
        Map<String, Object> result = new HashMap<>();
        
        // 风格分布
        Map<String, Double> styleDistribution = new HashMap<>();
        styleDistribution.put("casual", 0.6);
        styleDistribution.put("formal", 0.3);
        styleDistribution.put("sporty", 0.1);
        result.put("styleDistribution", styleDistribution);
        
        // 主导颜色
        result.put("dominantColors", new String[]{"白色", "蓝色"});
        
        // 推荐风格
        result.put("recommendedStyles", new String[]{"休闲", "商务休闲"});
        
        // 材质分析
        Map<String, Double> materialAnalysis = new HashMap<>();
        materialAnalysis.put("棉质", 0.7);
        materialAnalysis.put("聚酯纤维", 0.3);
        result.put("materialAnalysis", materialAnalysis);
        
        // 图案分析
        result.put("patternAnalysis", new String[]{"纯色", "简约"});
        
        // 季节适应性
        Map<String, Double> seasonSuitability = new HashMap<>();
        seasonSuitability.put("spring", 0.8);
        seasonSuitability.put("summer", 0.9);
        seasonSuitability.put("autumn", 0.6);
        seasonSuitability.put("winter", 0.4);
        result.put("seasonSuitability", seasonSuitability);
        
        // 场合适应性
        Map<String, Double> occasionSuitability = new HashMap<>();
        occasionSuitability.put("casual", 0.9);
        occasionSuitability.put("business", 0.6);
        occasionSuitability.put("formal", 0.3);
        result.put("occasionSuitability", occasionSuitability);
        
        log.info("风格分析完成，图片: {}, 结果: {}", imageUrl, result);
        return result;
    }

    @Override
    public Map<String, Object> analyzeColor(String imageUrl) {
        // 模拟色彩分析结果
        Map<String, Object> result = new HashMap<>();
        result.put("dominantColors", new String[]{"#FFFFFF", "#0066CC"});
        result.put("colorHarmony", 0.85);
        result.put("seasonSuitability", Map.of("spring", 0.8, "summer", 0.9, "autumn", 0.6, "winter", 0.7));
        
        log.info("色彩分析完成，图片: {}, 结果: {}", imageUrl, result);
        return result;
    }

    @Override
    public Map<String, Object> chat(String message, String context) {
        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", new Object[]{
                Map.of("role", "system", "content", "你是一个专业的时尚搭配顾问，请为用户提供专业的穿搭建议。"),
                Map.of("role", "user", "content", message)
            });
            requestBody.put("max_tokens", 500);
            requestBody.put("temperature", 0.7);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = baseUrl + "/v1/chat/completions";
            Map<String, Object> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class).getBody();

            // 解析响应
            Map<String, Object> result = new HashMap<>();
            if (response != null && response.containsKey("choices")) {
                Object[] choices = (Object[]) response.get("choices");
                if (choices.length > 0) {
                    Map<String, Object> choice = (Map<String, Object>) choices[0];
                    Map<String, Object> messageObj = (Map<String, Object>) choice.get("message");
                    result.put("reply", messageObj.get("content"));
                }
            }

            // 添加使用信息
            if (response != null && response.containsKey("usage")) {
                Map<String, Object> usage = (Map<String, Object>) response.get("usage");
                result.put("tokensUsed", usage.get("total_tokens"));
            }

            result.put("model", model);
            
            log.info("AI对话完成，消息: {}, 回复: {}", message, result.get("reply"));
            return result;

        } catch (Exception e) {
            log.error("AI对话失败", e);
            // 返回默认回复
            Map<String, Object> result = new HashMap<>();
            result.put("reply", "抱歉，我现在无法为您提供建议，请稍后再试。");
            result.put("model", model);
            return result;
        }
    }

    @Override
    public Map<String, Object> evaluateAppearance(String[] imageUrls, String evaluationType) {
        // 模拟形象评估结果
        Map<String, Object> result = new HashMap<>();
        result.put("overallScore", 85.0);
        result.put("styleScore", 88.0);
        result.put("colorScore", 82.0);
        result.put("fitScore", 85.0);
        result.put("suggestion", "整体搭配协调，建议增加一些亮色配饰来提升整体效果。");
        
        Map<String, Object> detailedAnalysis = new HashMap<>();
        detailedAnalysis.put("strengths", new String[]{"色彩搭配和谐", "风格统一"});
        detailedAnalysis.put("improvements", new String[]{"可以尝试更多层次感", "配饰可以更丰富"});
        result.put("detailedAnalysis", detailedAnalysis);
        
        log.info("形象评估完成，类型: {}, 评分: {}", evaluationType, result.get("overallScore"));
        return result;
    }

    @Override
    public Map<String, Object> rateOutfit(Map<String, Object> outfitData) {
        // 模拟穿搭评分结果
        Map<String, Object> result = new HashMap<>();
        result.put("aiScore", 82.5);
        result.put("finalScore", 85.0);
        
        Map<String, Double> scoreBreakdown = new HashMap<>();
        scoreBreakdown.put("colorHarmony", 85.0);
        scoreBreakdown.put("styleCoordination", 88.0);
        scoreBreakdown.put("seasonSuitability", 80.0);
        scoreBreakdown.put("occasionMatch", 87.0);
        result.put("scoreBreakdown", scoreBreakdown);
        
        result.put("improvementSuggestions", new String[]{
            "可以尝试添加一个亮色配饰",
            "鞋子的颜色可以与包包呼应"
        });
        
        log.info("穿搭评分完成，AI评分: {}, 最终评分: {}", result.get("aiScore"), result.get("finalScore"));
        return result;
    }

    @Override
    public Map<String, Object> analyzeBodyShape(MultipartFile imageFile) {
        // OpenAI客户端支持基本的身材识别
        Map<String, Object> result = new HashMap<>();
        result.put("aiResponse", "{\"isFullBodyPhoto\": true, \"confidence\": 0.85, \"bodyType\": \"沙漏形\", \"bodyShape\": {\"shoulderWidth\": 4, \"waistShape\": 4, \"belly\": 2, \"hip\": 4, \"hipShape\": 4, \"armLength\": 3, \"armCircum\": 3, \"hipWidth\": 4, \"thigh\": 3, \"calf\": 3, \"bodyFat\": 3, \"bodyLength\": 3}, \"analysis\": [{\"feature\": \"肩膀\", \"description\": \"肩膀较宽，有很好的线条感\", \"type\": \"优势\"}, {\"feature\": \"腰部\", \"description\": \"腰线明显，比例协调\", \"type\": \"优势\"}], \"suggestions\": [{\"category\": \"上装\", \"content\": \"建议选择修身款式突出腰线\", \"priority\": \"高\"}, {\"category\": \"下装\", \"content\": \"A字型裙子或直筒裤都很适合\", \"priority\": \"中\"}]}");
        result.put("model", "openai-vision-model");
        result.put("confidence", 85);
        result.put("tokensUsed", 150);
        
        log.info("OpenAI身材识别完成，置信度: {}", result.get("confidence"));
        return result;
    }

    @Override
    public Map<String, Object> analyzeBodyShapeByUrl(String imageUrl) {
        // OpenAI客户端支持基本的身材识别（URL方式）
        log.info("OpenAI客户端开始身材识别（URL方式）: {}", imageUrl);
        
        Map<String, Object> result = new HashMap<>();
        result.put("aiResponse", "{\"isFullBodyPhoto\": true, \"confidence\": 0.85, \"bodyType\": \"沙漏形\", \"bodyShape\": {\"shoulderWidth\": 4, \"waistShape\": 4, \"belly\": 2, \"hip\": 4, \"hipShape\": 4, \"armLength\": 3, \"armCircum\": 3, \"hipWidth\": 4, \"thigh\": 3, \"calf\": 3, \"bodyFat\": 3, \"bodyLength\": 3}, \"analysis\": [{\"feature\": \"肩膀\", \"description\": \"肩膀较宽，有很好的线条感\", \"type\": \"优势\"}, {\"feature\": \"腰部\", \"description\": \"腰线明显，比例协调\", \"type\": \"优势\"}], \"suggestions\": [{\"category\": \"上装\", \"content\": \"建议选择修身款式突出腰线\", \"priority\": \"高\"}, {\"category\": \"下装\", \"content\": \"A字型裙子或直筒裤都很适合\", \"priority\": \"中\"}]}");
        result.put("model", "openai-vision-model");
        result.put("confidence", 85);
        result.put("tokensUsed", 150);
        result.put("imageUrl", imageUrl);
        
        log.info("OpenAI身材识别完成（URL方式），置信度: {}", result.get("confidence"));
        return result;
    }

    @Override
    public Map<String, Object> calculateEnergyInfo(Map<String, Object> request) {
        // OpenAI客户端暂不支持能量计算，返回默认数据
        log.warn("OpenAI客户端暂不支持能量计算功能，返回默认数据");
        
        Map<String, Object> energyData = new HashMap<>();
        
        // 日期信息
        Map<String, Object> dateInfo = new HashMap<>();
        dateInfo.put("gregorian", request.get("date"));
        dateInfo.put("lunar", "农历信息");
        energyData.put("dateInfo", dateInfo);
        
        // 基本能量数据
        energyData.put("totalScore", 75);
        energyData.put("percentage", 60);
        energyData.put("peakTime", "上午8-10点");
        energyData.put("peakTimeDescription", "适合重要决策和创意工作");
        energyData.put("description", "今日能量平衡，整体运势平稳");
        
        // 五维能量评分
        Map<String, Object> dimensions = new HashMap<>();
        dimensions.put("love", 75);
        dimensions.put("career", 80);
        dimensions.put("wealth", 70);
        dimensions.put("health", 85);
        dimensions.put("relationship", 75);
        energyData.put("dimensions", dimensions);
        
        // 宜忌指南
        Map<String, Object> advice = new HashMap<>();
        advice.put("categories", new Object[]{});
        advice.put("lifeSuggestions", new Object[]{});
        energyData.put("advice", advice);
        
        // 幸运元素
        Map<String, Object> luckyElements = new HashMap<>();
        luckyElements.put("colors", new String[]{"#4ECDC4", "#FF6B6B"});
        luckyElements.put("clothing", new String[]{"推荐穿着舒适的服装"});
        luckyElements.put("accessories", new String[]{"简约的配饰"});
        luckyElements.put("makeup", new String[]{"清淡自然的妆容"});
        energyData.put("luckyElements", luckyElements);
        
        return energyData;
    }

    @Override
    public Map<String, Object> generateRecommendation(Map<String, Object> request) {
        log.warn("OpenAI客户端暂不支持穿搭推荐功能，返回错误信息");
        throw new RuntimeException("OpenAI客户端暂不支持穿搭推荐功能，请使用DeepSeek客户端");
    }
}