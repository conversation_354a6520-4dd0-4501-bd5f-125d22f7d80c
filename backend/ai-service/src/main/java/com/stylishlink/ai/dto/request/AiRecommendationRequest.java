package com.stylishlink.ai.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * AI推荐请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI推荐请求")
public class AiRecommendationRequest {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "推荐日期", example = "2024-01-01")
    private LocalDate date;

    @Schema(description = "使用场景", example = "[\"商务会议\", \"日常通勤\"]")
    private List<String> occasions;

    @Schema(description = "天气信息")
    private WeatherInfo weatherInfo;

    @Schema(description = "五行信息")
    private WuxingInfo wuxingInfo;

    @Schema(description = "用户偏好")
    private UserPreferences userPreferences;

    /**
     * 天气信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "天气信息")
    public static class WeatherInfo {
        @Schema(description = "温度", example = "25.5")
        private Double temperature;

        @Schema(description = "天气状况", example = "晴天")
        private String condition;

        @Schema(description = "湿度", example = "60")
        private Integer humidity;

        @Schema(description = "风速", example = "3.2")
        private Double windSpeed;

        @Schema(description = "紫外线指数", example = "5")
        private Integer uvIndex;

        @Schema(description = "穿衣指数", example = "适宜轻装")
        private String clothingIndex;
    }

    /**
     * 五行信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "五行信息")
    public static class WuxingInfo {
        @Schema(description = "八字", example = "庚午 戊子 甲寅 丙寅")
        private String baziString;

        @Schema(description = "五行分布")
        private Map<String, Integer> wuxingDistribution;

        @Schema(description = "日主", example = "甲")
        private String dayMaster;

        @Schema(description = "旺相休囚死")
        private Map<String, String> wuxingStrength;

        @Schema(description = "喜用神", example = "[\"水\", \"木\"]")
        private List<String> favorableElements;

        @Schema(description = "忌神", example = "[\"火\", \"土\"]")
        private List<String> unfavorableElements;
    }

    /**
     * 用户偏好
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户偏好")
    public static class UserPreferences {
        @Schema(description = "喜欢的风格", example = "[\"简约\", \"商务\"]")
        private List<String> favoriteStyles;

        @Schema(description = "喜欢的颜色", example = "[\"白色\", \"蓝色\"]")
        private List<String> favoriteColors;

        @Schema(description = "不喜欢的风格", example = "[\"嘻哈\", \"朋克\"]")
        private List<String> dislikedStyles;

        @Schema(description = "不喜欢的颜色", example = "[\"红色\", \"橙色\"]")
        private List<String> dislikedColors;

        @Schema(description = "身高", example = "170")
        private Integer height;

        @Schema(description = "体重", example = "60")
        private Integer weight;

        @Schema(description = "体型", example = "标准")
        private String bodyShape;

        @Schema(description = "肤色", example = "偏白")
        private String skinTone;

        @Schema(description = "场合偏好")
        private List<String> occasionPreferences;
    }
}
