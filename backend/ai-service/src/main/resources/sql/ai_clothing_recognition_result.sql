-- 衣物识别结果表建表脚本

DROP TABLE IF EXISTS `ai_clothing_recognition_result`;

CREATE TABLE `ai_clothing_recognition_result` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `recognition_id` VARCHAR(64) NOT NULL COMMENT '识别结果ID',
    `user_id` VARCHAR(64) COMMENT '用户ID',
    `file_id` VARCHAR(64) COMMENT '文件ID',
    `image_url` VARCHAR(500) COMMENT '图片URL',
    `name` VARCHAR(100) COMMENT '衣物名称',
    `category` VARCHAR(64) COMMENT '大类别',
    `sub_category` VARCHAR(64) COMMENT '子类别',
    `colors` JSON COMMENT '颜色列表',
    `materials` JSON COMMENT '材质列表',
    `seasons` JSON COMMENT '适合季节',
    `occasions` JSON COMMENT '适合场合',
    `wuxing_metal` INT DEFAULT 0 COMMENT '金属性评分(0-4)',
    `wuxing_wood` INT DEFAULT 0 COMMENT '木属性评分(0-4)',
    `wuxing_water` INT DEFAULT 0 COMMENT '水属性评分(0-4)',
    `wuxing_fire` INT DEFAULT 0 COMMENT '火属性评分(0-4)',
    `wuxing_earth` INT DEFAULT 0 COMMENT '土属性评分(0-4)',
    `wuxing_primary` VARCHAR(10) COMMENT '主要五行属性',
    `confidence` DECIMAL(5,4) COMMENT '识别置信度(0-1)',
    `suggestions` JSON COMMENT '穿搭建议',
    `raw_result` JSON COMMENT '原始AI响应结果',
    `processed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_recognition_id` (`recognition_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_file_id` (`file_id`),
    INDEX `idx_category` (`category`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='衣物识别结果表'; 