<!-- 五行相生相克圆形UI组件 - 微信小程序Canvas版本 -->
<script setup lang="ts">
import { computed, getCurrentInstance, inject, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

// 五行数据类型定义
interface WuxingData {
  element: '金' | '木' | '水' | '火' | '土'
  percentage: number
  isHighlight?: boolean // 是否高亮显示（如日主）
}

// Props定义
interface Props {
  /** 五行分布数据 */
  wuxingData: WuxingData[]
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否显示相生相克线条 */
  showRelations?: boolean
  /** 是否显示文字标注 */
  showLabels?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  showRelations: true,
  showLabels: true,
})

// 五行顺序（按正五角星顶点排列：火在顶部，顺时针排列）
const wuxingOrder = ['火', '土', '金', '水', '木'] as const

// 五行颜色配置
const wuxingColors = {
  金: {
    background: '#f3f4f6',
    border: '#d1d5db',
    text: '#374151',
  },
  木: {
    background: '#e0f2e9',
    border: '#6ee7b7',
    text: '#059669',
  },
  水: {
    background: '#dbeafe',
    border: '#93c5fd',
    text: '#2563eb',
  },
  火: {
    background: '#fee2e2',
    border: '#fca5a5',
    text: '#dc2626',
  },
  土: {
    background: '#fef3c7',
    border: '#fcd34d',
    text: '#d97706',
  },
}

// 组件尺寸配置
const sizeConfig = {
  small: {
    canvasSize: 200,
    nodeRadius: 16,
    fontSize: 10,
  },
  medium: {
    canvasSize: 280,
    nodeRadius: 24,
    fontSize: 12,
  },
  large: {
    canvasSize: 320,
    nodeRadius: 32,
    fontSize: 14,
  },
}

// Canvas相关
const canvasId = `wuxing-canvas-${Date.now()}`
const instance = getCurrentInstance()
const canvasReady = ref(false)
let retryCount = 0
const maxRetry = 3

// Canvas绘制状态管理
const isDrawing = ref(false)
const isScrolling = ref(false) // 🚀 滚动状态管理

// 🚀 获取全局滚动状态
const isPageScrolling = inject<any>('isPageScrolling', ref(false))

// 🚀 最简单有效方案：滚动时完全禁用Canvas重绘
const isUserScrolling = ref(false) // 用户主动滚动状态
const isCanvasStable = ref(true) // Canvas稳定状态
const isScrollBlocked = ref(false) // 🚀 滚动阻塞状态
let scrollTimer: number | null = null
let stabilityTimer: number | null = null
let blockTimer: number | null = null

// 🚀 iOS设备检测缓存
let isIOSDeviceCache: boolean | null = null

// 根据元素名获取数据
function getWuxingData(element: string) {
  return props.wuxingData.find(item => item.element === element) || {
    element: element as any,
    percentage: 0,
    isHighlight: false,
  }
}

// 计算容器样式类
const containerClass = computed(() => {
  return 'wuxing-container relative mx-auto'
})

// 计算容器样式
const containerStyle = computed(() => {
  const config = sizeConfig[props.size]
  return {
    width: `${config.canvasSize}px`,
    height: `${config.canvasSize}px`,
  }
})

// Canvas初始化检查
function checkCanvasReady() {
  try {
    const ctx = uni.createCanvasContext(canvasId, instance)
    if (ctx) {
      canvasReady.value = true
      return true
    }
  }
  catch (error) {
    console.error('Canvas context creation failed:', error)
  }
  return false
}

// 计算五行节点位置（正五角星顶点）
function getNodePositions() {
  const config = sizeConfig[props.size]
  const centerX = config.canvasSize / 2
  const centerY = config.canvasSize / 2
  const radius = config.canvasSize * 0.35 // 五行节点圆的半径

  const nodePositions: { [key: string]: { x: number, y: number } } = {}
  wuxingOrder.forEach((element, index) => {
    // 正五角星的角度：从12点钟方向开始，每个顶点间隔72度
    const angle = (index * 72 - 90) * Math.PI / 180 // -90度让火在顶部
    nodePositions[element] = {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle),
    }
  })

  return nodePositions
}

// 绘制弧线（相生关系）
function drawArc(ctx: any, startPos: any, endPos: any, centerX: number, centerY: number, radius: number, color: string = '#6b7280') {
  const config = sizeConfig[props.size]
  const startAngle = Math.atan2(startPos.y - centerY, startPos.x - centerX)
  const endAngle = Math.atan2(endPos.y - centerY, endPos.x - centerX)

  // 处理角度跨越的情况，确保按顺时针方向绘制弧线
  let actualEndAngle = endAngle
  if (endAngle < startAngle) {
    actualEndAngle = endAngle + 2 * Math.PI
  }

  // 计算弧线起始和停止角度（距离节点边缘一定距离）
  const nodeRadius = config.nodeRadius
  const startDistance = nodeRadius + 12 // 弧线距离起始节点边缘16px开始
  const stopDistance = nodeRadius + 16 // 弧线距离目标节点边缘12px停止
  const startAngleOffset = startDistance / radius // 起始角度偏移量
  const stopAngleOffset = stopDistance / radius // 停止角度偏移量
  const arcStartAngle = startAngle + startAngleOffset
  const arcEndAngle = actualEndAngle - stopAngleOffset

  // 绘制弧线（从起始节点边缘开始，不完全到达目标节点）
  ctx.setStrokeStyle(color)
  ctx.setLineWidth(2)
  ctx.setLineCap('round')
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius, arcStartAngle, arcEndAngle, false)
  ctx.stroke()

  // 计算箭头位置：弧线的实际终点
  const arrowX = centerX + radius * Math.cos(arcEndAngle)
  const arrowY = centerY + radius * Math.sin(arcEndAngle)

  // 箭头方向：沿弧线切线方向（垂直于半径方向）
  const tangentAngle = arcEndAngle - Math.PI

  ctx.save()
  ctx.setFillStyle(color)
  ctx.translate(arrowX, arrowY)
  ctx.rotate(tangentAngle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6 // 根据尺寸调整箭头大小
  ctx.beginPath()
  ctx.moveTo(0, -arrowSize)
  ctx.lineTo(-arrowSize / 2, arrowSize / 2)
  ctx.lineTo(arrowSize / 2, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在弧线中间绘制"生"字标注（使用动态字号）
  // 使用实际弧线的中点角度
  const midAngle = (arcStartAngle + arcEndAngle) / 2
  const textRadius = radius + 15 // 文字距离弧线外侧适中距离
  const textX = centerX + textRadius * Math.cos(midAngle)
  const textY = centerY + textRadius * Math.sin(midAngle)

  ctx.save()
  ctx.setFillStyle(color)
  const labelFontSize = Math.max(8, config.fontSize - 2) // 关系标注字号比节点字号小2px，最小8px
  ctx.setFontSize(labelFontSize)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')
  ctx.fillText('生', textX, textY)
  ctx.restore()
}

// 绘制虚线（相克关系）
function drawDashedLine(ctx: any, startPos: any, endPos: any, color: string = '#9ca3af') {
  const config = sizeConfig[props.size]
  const nodeRadius = config.nodeRadius

  // 计算方向向量
  const dx = endPos.x - startPos.x
  const dy = endPos.y - startPos.y
  const length = Math.sqrt(dx * dx + dy * dy)
  const unitX = dx / length
  const unitY = dy / length

  // 调整起点和终点，避免与节点重叠
  const adjustedStartX = startPos.x + unitX * (nodeRadius + 10)
  const adjustedStartY = startPos.y + unitY * (nodeRadius + 10)
  const adjustedEndX = endPos.x - unitX * (nodeRadius + 10)
  const adjustedEndY = endPos.y - unitY * (nodeRadius + 10)

  ctx.setStrokeStyle(color)
  ctx.setLineWidth(2)
  ctx.setLineDash([6, 4])
  ctx.beginPath()
  ctx.moveTo(adjustedStartX, adjustedStartY)
  ctx.lineTo(adjustedEndX, adjustedEndY)
  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线设置

  // 在虚线末端绘制箭头（距离目标节点适当距离）
  const arrowDistance = nodeRadius + 8 // 箭头距离节点边缘8px
  const arrowX = endPos.x - unitX * arrowDistance
  const arrowY = endPos.y - unitY * arrowDistance
  const angle = Math.atan2(dy, dx)

  ctx.save()
  ctx.setFillStyle(color)
  ctx.translate(arrowX, arrowY)
  ctx.rotate(angle)

  // 绘制三角形箭头
  const arrowSize = config.canvasSize > 250 ? 8 : 6 // 根据尺寸调整箭头大小
  ctx.beginPath()
  ctx.moveTo(0, 0)
  ctx.lineTo(-arrowSize, -arrowSize / 2)
  ctx.lineTo(-arrowSize, arrowSize / 2)
  ctx.closePath()
  ctx.fill()

  ctx.restore()

  // 在虚线中间绘制"克"字标注（使用动态字号）
  const midX = (adjustedStartX + adjustedEndX) / 2
  const midY = (adjustedStartY + adjustedEndY) / 2

  // 计算垂直于虚线的方向向量，向外偏移
  const offsetDistance = 12 // "克"字距离虚线的外侧距离
  const perpX = unitY // 垂直方向（向右旋转90度）
  const perpY = -unitX

  // "克"字位置：虚线中点向外偏移
  const textX = midX + perpX * offsetDistance
  const textY = midY + perpY * offsetDistance

  ctx.save()
  ctx.setFillStyle(color)
  const labelFontSize = Math.max(8, config.fontSize - 2) // 关系标注字号比节点字号小2px，最小8px
  ctx.setFontSize(labelFontSize)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')
  ctx.fillText('克', textX, textY)
  ctx.restore()
}

// 绘制五行相生相克图
function drawWuxingChart() {
  // 🚀 激进滚动检测：任何滚动状态都跳过重绘
  if (isAnyScrolling()) {
    console.warn('WuxingCircle: 检测到滚动状态，跳过重绘', {
      isDrawing: isDrawing.value,
      isScrolling: isScrolling.value,
      isPageScrolling: isPageScrolling.value,
      isUserScrolling: isUserScrolling.value,
      isCanvasStable: isCanvasStable.value,
    })
    return
  }

  // 防止重复绘制
  if (isDrawing.value) {
    console.warn('WuxingCircle: 正在绘制中，跳过重绘')
    return
  }

  if (!canvasReady.value && !checkCanvasReady()) {
    console.warn('Canvas not ready, will retry...')
    if (retryCount < maxRetry) {
      retryCount++
      setTimeout(() => {
        drawWuxingChart()
      }, 1000 * retryCount)
    }
    return
  }

  isDrawing.value = true

  const ctx = uni.createCanvasContext(canvasId, instance)
  if (!ctx) {
    console.error('Failed to get canvas context')
    isDrawing.value = false
    return
  }

  const config = sizeConfig[props.size]
  const centerX = config.canvasSize / 2
  const centerY = config.canvasSize / 2
  const radius = config.canvasSize * 0.35

  // 清空画布
  ctx.clearRect(0, 0, config.canvasSize, config.canvasSize)

  // 获取节点位置
  const positions = getNodePositions()

  if (props.showRelations) {
    // 1. 绘制相生关系（绿色弧线+箭头）
    // 相生顺序：木→火→土→金→水→木
    const shengPairs = [
      ['木', '火'],
      ['火', '土'],
      ['土', '金'],
      ['金', '水'],
      ['水', '木'],
    ]

    shengPairs.forEach(([from, to]) => {
      const fromPos = positions[from]
      const toPos = positions[to]
      drawArc(ctx, fromPos, toPos, centerX, centerY, radius)
    })

    // 2. 绘制相克关系（红色虚线）
    // 相克关系：火克金、金克木、木克土、土克水、水克火
    const kePairs = [
      ['火', '金'],
      ['金', '木'],
      ['木', '土'],
      ['土', '水'],
      ['水', '火'],
    ]

    kePairs.forEach(([from, to]) => {
      const fromPos = positions[from]
      const toPos = positions[to]
      drawDashedLine(ctx, fromPos, toPos)
    })
  }

  // 3. 绘制五行节点
  wuxingOrder.forEach((element) => {
    const pos = positions[element]
    const data = getWuxingData(element)
    const colors = wuxingColors[element]
    const nodeRadius = config.nodeRadius

    // 绘制节点圆形背景
    ctx.setFillStyle(colors.background)
    ctx.setStrokeStyle(data.isHighlight ? colors.text : colors.border)
    ctx.setLineWidth(data.isHighlight ? 3 : 2)

    ctx.beginPath()
    ctx.arc(pos.x, pos.y, nodeRadius, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()

    // 绘制文字
    ctx.setFillStyle(colors.text)
    ctx.setTextAlign('center')
    ctx.setTextBaseline('middle')

    // 绘制五行名称（保持原始位置）
    ctx.setFontSize(config.fontSize)
    const nameOffsetY = nodeRadius * 0.25 // 五行名称向上偏移
    ctx.fillText(element, pos.x, pos.y - nameOffsetY)

    // 绘制百分比（保持原始位置）
    ctx.setFontSize(config.fontSize - 2)
    const percentOffsetY = nodeRadius * 0.25 // 百分比向下偏移
    ctx.fillText(`${data.percentage}%`, pos.x, pos.y + percentOffsetY)

    // 如果是日主，在圆边缘下方添加"日主"标注
    if (data.isHighlight) {
      const masterFontSize = Math.max(8, config.fontSize - 2) // 比原字号小2号
      const masterOffsetY = nodeRadius - 2 // 圆边缘位置，稍微向内2px
      const textX = pos.x
      const textY = pos.y + masterOffsetY

      // 绘制"日主"文字背景
      ctx.setFontSize(masterFontSize)
      const textWidth = 32 // 加宽背景以凸显圆角
      const textHeight = masterFontSize + 8 // 增加上下padding
      const bgX = textX - textWidth / 2
      const bgY = textY - textHeight / 2

      // 绘制半透明白色圆角背景
      ctx.setFillStyle('rgba(255, 255, 255, 0.9)')
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.1)')
      ctx.setLineWidth(1)
      ctx.beginPath()

      // 手动绘制圆角矩形（兼容微信小程序）
      const radius = 8 // 增加圆角半径以凸显效果
      ctx.moveTo(bgX + radius, bgY)
      ctx.lineTo(bgX + textWidth - radius, bgY)
      ctx.arc(bgX + textWidth - radius, bgY + radius, radius, -Math.PI / 2, 0)
      ctx.lineTo(bgX + textWidth, bgY + textHeight - radius)
      ctx.arc(bgX + textWidth - radius, bgY + textHeight - radius, radius, 0, Math.PI / 2)
      ctx.lineTo(bgX + radius, bgY + textHeight)
      ctx.arc(bgX + radius, bgY + textHeight - radius, radius, Math.PI / 2, Math.PI)
      ctx.lineTo(bgX, bgY + radius)
      ctx.arc(bgX + radius, bgY + radius, radius, Math.PI, Math.PI * 3 / 2)
      ctx.closePath()

      ctx.fill()
      ctx.stroke()

      // 绘制"日主"文字
      ctx.setFillStyle(colors.text)
      ctx.setTextAlign('center')
      ctx.setTextBaseline('middle')
      ctx.fillText('日主', textX, textY)
    }
  })

  // 🚀 激进优化：绘制提交前最后一次滚动状态检查
  if (isAnyScrolling()) {
    console.warn('WuxingCircle: 绘制提交前检测到滚动，取消绘制')
    isDrawing.value = false
    return
  }

  ctx.draw(false, () => {
    // 绘制完成
    isDrawing.value = false
    console.warn('WuxingCircle: 绘制完成')

    // 🚀 绘制完成后，设置Canvas稳定状态
    setScrollingState(false, 'draw-complete')
  })
}

// Canvas错误处理
function handleCanvasError(e: any) {
  console.error('Canvas error:', e)
  // 出错时重试绘制
  setTimeout(() => {
    drawWuxingChart()
  }, 500)
}



// 防抖计时器
let debounceTimer: number | null = null

// 🚀 监听全局滚动状态变化
watch([isPageScrolling, isScrolling], ([newPageScrolling, newLocalScrolling]) => {
  if (newPageScrolling || newLocalScrolling) {
    setScrollingState(true, 'watch-scroll-state')
  }
}, { immediate: true })

// 监听属性变化重新绘制 - 激进滚动抖动优化
watch([() => props.wuxingData, () => props.size, () => props.showRelations], (newValues, oldValues) => {
  // 🚀 激进检查：任何滚动状态都跳过重绘
  if (isAnyScrolling()) {
    console.warn('WuxingCircle: 滚动状态中，跳过属性变化重绘')
    return
  }

  // 如果正在绘制，则跳过重绘
  if (isDrawing.value) {
    console.warn('WuxingCircle: 正在绘制中，跳过属性变化重绘')
    return
  }

  // 数据变化时的特殊处理
  const [newData, _newSize, _newRelations] = newValues
  const [oldData, _oldSize, _oldRelations] = oldValues || []

  // 检查是否为数据首次加载（从空到有数据）
  const isFirstDataLoad = (!oldData || oldData.length === 0) && (newData && newData.length > 0)

  if (isFirstDataLoad) {
    console.warn('WuxingCircle: 检测到数据首次加载，使用智能延时绘制')
    smartDelayedDraw()
    return
  }

  // 清除之前的计时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 🚀 激进防抖：iOS设备使用更长延迟
  const debounceDelay = isIOSDevice() ? 1200 : 800 // iOS设备使用1.2秒延迟

  debounceTimer = setTimeout(() => {
    // 再次检查滚动状态
    if (isAnyScrolling()) {
      console.warn('WuxingCircle: 防抖期间检测到滚动，跳过重绘')
      debounceTimer = null
      return
    }

    if (!checkDataReady()) {
      console.warn('WuxingCircle: 数据不完整，跳过重绘')
      debounceTimer = null
      return
    }

    // 使用requestAnimationFrame优化重绘时机
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(() => {
        nextTick(() => {
          // 最后一次滚动状态检查
          if (!isAnyScrolling()) {
            drawWuxingChart()
          }
        })
      })
    }
    else {
      nextTick(() => {
        if (!isAnyScrolling()) {
          drawWuxingChart()
        }
      })
    }
    debounceTimer = null
  }, debounceDelay)
}, { deep: true })

// 数据就绪检查函数
function checkDataReady(): boolean {
  const isReady = props.wuxingData && Array.isArray(props.wuxingData) && props.wuxingData.length > 0
  console.warn('WuxingCircle数据检查:', {
    hasWuxingData: !!props.wuxingData,
    isArray: Array.isArray(props.wuxingData),
    dataLength: props.wuxingData?.length || 0,
    dataContent: props.wuxingData,
    isReady,
  })
  return isReady
}

// iOS设备检测（带缓存）
function isIOSDevice(): boolean {
  if (isIOSDeviceCache === null) {
    try {
      const deviceInfo = uni.getDeviceInfo()
      isIOSDeviceCache = deviceInfo.platform === 'ios'
    }
    catch (error) {
      console.error('设备检测失败:', error)
      isIOSDeviceCache = false
    }
  }
  return isIOSDeviceCache
}

// 🚀 最严格滚动状态检测：任何滚动相关状态都阻止重绘
function isAnyScrolling(): boolean {
  return isScrolling.value || isPageScrolling.value || isUserScrolling.value || !isCanvasStable.value || isScrollBlocked.value
}

// 🚀 激进滚动阻塞：完全禁用滚动期间的重绘
function setScrollBlock(blocked: boolean, source: string = 'unknown') {
  console.warn(`WuxingCircle: 设置滚动阻塞 ${blocked} (来源: ${source})`)

  isScrollBlocked.value = blocked

  if (blockTimer) {
    clearTimeout(blockTimer)
    blockTimer = null
  }

  if (blocked) {
    // 🚀 iOS设备使用超长阻塞时间
    const blockDelay = isIOSDevice() ? 2000 : 1000 // iOS设备2秒，Android设备1秒

    blockTimer = setTimeout(() => {
      isScrollBlocked.value = false
      console.warn(`WuxingCircle: 滚动阻塞解除 (${blockDelay}ms)`)
      blockTimer = null
    }, blockDelay)
  }
}

// 🚀 设置滚动状态（完全阻塞模式）
function setScrollingState(scrolling: boolean, source: string = 'unknown') {
  console.warn(`WuxingCircle: 设置滚动状态 ${scrolling} (来源: ${source})`)

  isUserScrolling.value = scrolling
  isCanvasStable.value = !scrolling

  // 清除之前的计时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }

  if (stabilityTimer) {
    clearTimeout(stabilityTimer)
    stabilityTimer = null
  }

  if (scrolling) {
    // 🚀 滚动开始：立即启动阻塞
    setScrollBlock(true, 'scroll-start')

    // 🚀 iOS设备使用超长稳定期
    const delay = isIOSDevice() ? 1500 : 800 // iOS设备1.5秒，Android设备0.8秒

    scrollTimer = setTimeout(() => {
      console.warn(`WuxingCircle: 滚动稳定期结束 (${delay}ms)`)
      isUserScrolling.value = false

      // 额外的稳定期，确保Canvas完全稳定
      stabilityTimer = setTimeout(() => {
        isCanvasStable.value = true
        console.warn('WuxingCircle: Canvas稳定状态恢复')
      }, 500) // 增加稳定期到500ms

      scrollTimer = null
    }, delay)
  }
}

// 智能延时绘制
function smartDelayedDraw() {
  console.warn('WuxingCircle: 开始智能延时绘制检查')

  if (!checkDataReady()) {
    console.warn('WuxingCircle: 数据未就绪，跳过绘制')
    return
  }

  // iOS设备使用更长延时，确保渲染完成
  const delay = isIOSDevice() ? 800 : 300

  setTimeout(() => {
    if (checkDataReady()) {
      console.warn('WuxingCircle: 开始绘制，延时:', `${delay}ms`)
      drawWuxingChart()
    }
    else {
      console.warn('WuxingCircle: 延时后数据仍未就绪')
    }
  }, delay)
}

// 🚀 页面滚动监听器
let pageScrollListener: (() => void) | null = null

// 🚀 添加页面滚动监听
function addPageScrollListener() {
  if (pageScrollListener) return

  pageScrollListener = () => {
    setScrollingState(true, 'page-scroll-listener')
  }

  // 尝试监听页面滚动事件
  try {
    // 微信小程序环境下监听页面滚动
    if (typeof uni !== 'undefined' && uni.onPageScroll) {
      uni.onPageScroll(pageScrollListener)
    }
    // 或者监听window滚动（Web环境）
    else if (typeof window !== 'undefined') {
      window.addEventListener('scroll', pageScrollListener, { passive: true })
    }
  }
  catch (error) {
    console.warn('WuxingCircle: 页面滚动监听器添加失败', error)
  }
}

// 🚀 移除页面滚动监听
function removePageScrollListener() {
  if (!pageScrollListener) return

  try {
    // 微信小程序环境下移除监听
    if (typeof uni !== 'undefined' && uni.offPageScroll) {
      uni.offPageScroll(pageScrollListener)
    }
    // Web环境下移除监听
    else if (typeof window !== 'undefined') {
      window.removeEventListener('scroll', pageScrollListener)
    }
  }
  catch (error) {
    console.warn('WuxingCircle: 页面滚动监听器移除失败', error)
  }

  pageScrollListener = null
}

// 组件挂载时初始化
onMounted(() => {
  console.warn('WuxingCircle: 组件挂载，开始初始化')

  // 添加页面滚动监听
  addPageScrollListener()

  nextTick(() => {
    smartDelayedDraw()
  })
})

// 🚀 组件卸载时清理
onUnmounted(() => {
  console.warn('WuxingCircle: 组件卸载，开始清理')

  // 清理所有计时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }

  if (stabilityTimer) {
    clearTimeout(stabilityTimer)
    stabilityTimer = null
  }

  if (debounceTimer) {
    clearTimeout(debounceTimer)
    debounceTimer = null
  }

  // 移除页面滚动监听
  removePageScrollListener()

  // 重置状态
  isDrawing.value = false
  isScrolling.value = false
  isUserScrolling.value = false
  isCanvasStable.value = true
})
</script>

<template>
  <view :class="containerClass" :style="containerStyle">
    <!-- 🚀 Canvas图形（非滚动时显示） -->
    <canvas
      v-show="!showImage"
      :id="canvasId"
      :canvas-id="canvasId"
      class="wuxing-canvas"
      :force-use-old-canvas="true"
      :style="{
        width: `${sizeConfig[size].canvasSize}px`,
        height: `${sizeConfig[size].canvasSize}px`,
      }"
      @error="handleCanvasError"
    />

    <!-- 🚀 静态图片（滚动时显示，消除抖动） -->
    <image
      v-show="showImage && imageReady && canvasImageUrl"
      :src="canvasImageUrl"
      class="wuxing-image"
      mode="aspectFit"
      :style="{
        width: `${sizeConfig[size].canvasSize}px`,
        height: `${sizeConfig[size].canvasSize}px`,
      }"
      @error="handleImageError"
    />
  </view>
</template>

<style scoped lang="scss">
.wuxing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;

  // 🚀 激进Canvas滚动性能优化
  will-change: transform; /* 明确告知浏览器会发生变换 */
  contain: strict; /* 最严格的包含，完全隔离重绘和重排 */
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  -webkit-transform: translate3d(0, 0, 0);

  // 🚀 防止滚动时的层级抖动
  isolation: isolate;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;

  // 🚀 优化合成层
  -webkit-perspective: 1000px;
  perspective: 1000px;

  // 🚀 iOS专用优化
  -webkit-touch-callout: none; /* 禁用iOS长按菜单 */
  -webkit-user-select: none; /* 禁用文本选择 */
  user-select: none;
  -webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */

  // 🚀 强制硬件加速
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;

  // 🚀 优化渲染性能
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wuxing-canvas {
  display: block;

  // 🚀 激进Canvas滚动性能优化
  will-change: transform; /* 明确告知会发生变换 */
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  -webkit-transform: translate3d(0, 0, 0);
  contain: strict; /* 最严格的包含，防止影响其他元素 */

  // 🚀 防止Canvas重绘抖动
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;

  // 🚀 优化Canvas渲染
  image-rendering: -webkit-optimize-contrast; /* iOS优化 */
  image-rendering: crisp-edges;
  image-rendering: pixelated; /* 防止模糊 */

  // 🚀 防止滚动时的内容跳动
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // 🚀 iOS专用Canvas优化
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  // 🚀 强制独立合成层
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;

  // 🚀 优化Canvas交互
  pointer-events: none; /* Canvas不响应触摸事件，避免干扰滚动 */

  // 🚀 防止iOS Safari的Canvas缩放问题
  -webkit-user-scalable: no;
  -webkit-text-size-adjust: 100%;
}

// 🚀 静态图片样式（与Canvas保持一致）
.wuxing-image {
  display: block;

  // 🚀 与Canvas相同的性能优化
  will-change: transform;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  contain: strict;

  // 🚀 防止图片重绘抖动
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;

  // 🚀 优化图片渲染
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;

  // 🚀 防止滚动时的内容跳动
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // 🚀 iOS专用图片优化
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  // 🚀 强制独立合成层
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;

  // 🚀 优化图片交互
  pointer-events: none; /* 图片不响应触摸事件，避免干扰滚动 */

  // 🚀 防止iOS Safari的图片缩放问题
  -webkit-user-scalable: no;
  -webkit-text-size-adjust: 100%;
}
</style>
