<!-- 开发测试页面 - 样式测试、组件测试、主题测试 -->
<script setup lang="ts">
// 导入类型
import type { AdviceItem, CaseItem, EnergyData, LocationInfo, LunarInfo, WeatherData } from '@/types/business'
import { onMounted, provide, ref } from 'vue'
// 导入业务组件
import CaseGrid from '@/components/business/CaseGrid.vue'
import OutfitRecommendationCard from '@/components/business/OutfitRecommendationCard.vue'
import WeatherEnergySection from '@/components/business/WeatherEnergySection.vue'
import WuxingCircle from '@/components/business/WuxingCircle.vue'
import CategoryTabs from '@/components/common/CategoryTabs.vue'
import CitySelector from '@/components/common/CitySelector.vue'
import DevTools from '@/components/common/DevTools.vue'
// 导入所有通用组件
import EnvironmentSwitcher from '@/components/common/EnvironmentSwitcher.vue'

import FloatingActionButton from '@/components/common/FloatingActionButton.vue'
import GlassCard from '@/components/common/GlassCard.vue'
import IconButton from '@/components/common/IconButton.vue'
import LikeFavoriteButton from '@/components/common/LikeFavoriteButton.vue'
import PageHeader from '@/components/common/PageHeader.vue'
import StarRating from '@/components/common/StarRating.vue'
// 导入工具函数
import { useMenuButton } from '@/composables/useMenuButton'

// 胶囊按钮动态适配
const { contentOffset12px } = useMenuButton()

// 🚀 全局滚动状态管理（用于Canvas组件优化）
const isPageScrolling = ref(false)
let scrollTimer: number | null = null

// 🚀 提供全局滚动状态给子组件
provide('isPageScrolling', isPageScrolling)

// 响应式数据
const currentTab = ref('热门')
const rating = ref(4.5)
const testSelectedCity = ref('北京')

// 能量数据
const energyData: EnergyData = {
  score: 78,
  percentage: 82,
}

// 建议列表
const adviceList: AdviceItem[] = [
  {
    id: '1',
    type: 'fashion',
    title: '选择木属性服饰',
    description: '今日适合选择绿色系服装',
    icon: 'icon-leaf',
    backgroundColor: '#22c55e',
  },
  {
    id: '2',
    type: 'accessory',
    title: '搭配绿色配饰',
    description: '绿色配饰能提升运势',
    icon: 'icon-accessories',
    backgroundColor: '#10b981',
  },
  {
    id: '3',
    type: 'makeup',
    title: '避免红色单品',
    description: '红色与今日能量冲突',
    icon: 'icon-warning',
    backgroundColor: '#ef4444',
  },
]

// 案例数据
const caseList: CaseItem[] = [
  {
    id: 1,
    title: '清新田园风',
    image: '/static/images/case1.jpg',
    rating: 4.8,
    likes: 156,
    favorites: 89,
    liked: false,
    favorited: true,
    tags: ['清新', '田园'],
  },
  {
    id: 2,
    title: '优雅职场装',
    image: '/static/images/case2.jpg',
    rating: 4.6,
    likes: 89,
    favorites: 67,
    liked: true,
    favorited: false,
    tags: ['职场', '优雅'],
  },
]

// 天气数据
const weatherData: WeatherData = {
  temperature: 22,
  condition: '晴朗',
  icon: 'icon-sunny',
  humidity: 65,
  windSpeed: 12,
}

// 位置信息
const locationInfo: LocationInfo = {
  city: '北京',
  district: '朝阳区',
  province: '北京市',
}

// 农历信息
const lunarInfo: LunarInfo = {
  month: '十月',
  day: '初八',
  year: '甲辰年',
  fullDate: '甲辰年十月初八',
}

// 分类标签
const categoryTabs = [
  { key: '热门', label: '热门' },
  { key: '推荐', label: '推荐' },
  { key: '新品', label: '新品' },
  { key: '搭配', label: '搭配' },
  { key: '单品', label: '单品' },
]

// 五行测试数据
const wuxingTestData = [
  { element: '木' as const, percentage: 46, isHighlight: true }, // 日主
  { element: '火' as const, percentage: 8, isHighlight: false },
  { element: '土' as const, percentage: 22, isHighlight: false },
  { element: '金' as const, percentage: 22, isHighlight: false },
  { element: '水' as const, percentage: 0, isHighlight: false }, // 缺水
]

const wuxingTestData2 = [
  { element: '木' as const, percentage: 25, isHighlight: false },
  { element: '火' as const, percentage: 30, isHighlight: true }, // 日主
  { element: '土' as const, percentage: 15, isHighlight: false },
  { element: '金' as const, percentage: 20, isHighlight: false },
  { element: '水' as const, percentage: 10, isHighlight: false },
]

onMounted(() => {
  // 测试页面加载完成
  console.warn('组件测试页面加载完成')
})

// 返回首页
function goHome() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}

// 事件处理
function handleFloatingClick() {
  uni.showToast({ title: '浮动按钮点击', icon: 'none' })
}

function handleDevToolsClick() {
  uni.showToast({ title: '开发工具点击', icon: 'none' })
}

function handleIconButtonClick(type: string) {
  uni.showToast({ title: `${type}按钮点击`, icon: 'none' })
}

function handleCategoryChange(key: string) {
  currentTab.value = key
  uni.showToast({ title: `切换到${key}`, icon: 'none' })
}

function handleEnergyTap() {
  uni.showToast({ title: '能量点击', icon: 'none' })
}

function handleAdviceTap(advice: AdviceItem) {
  uni.showToast({ title: `点击建议: ${advice.title}`, icon: 'none' })
}

function handleCaseLike(caseItem: CaseItem) {
  console.warn('点赞案例:', caseItem.title)
}

function handleCaseFavorite(caseItem: CaseItem) {
  console.warn('收藏案例:', caseItem.title)
}

function handleLocationTap() {
  uni.showToast({ title: '位置点击', icon: 'none' })
}

function handleStarRatingChange(newRating: number) {
  rating.value = newRating
  uni.showToast({ title: `评分: ${newRating}`, icon: 'none' })
}

// CitySelector测试方法
function handleTestCityChange(city: string) {
  console.warn('城市选择变更:', city)
  uni.showToast({ title: `选择城市: ${city}`, icon: 'none' })
}

// 🚀 滚动事件处理
function handleScroll() {
  // 立即设置滚动状态，阻止Canvas重绘
  isPageScrolling.value = true

  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 滚动结束后延迟恢复Canvas重绘
  scrollTimer = setTimeout(() => {
    isPageScrolling.value = false
    console.warn('测试页面滚动结束，恢复Canvas重绘')
  }, 300) // 300ms延迟确保滚动完全结束
}

function handleScrollEnd() {
  // 滚动到顶部或底部时，延迟恢复Canvas重绘
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  scrollTimer = setTimeout(() => {
    isPageScrolling.value = false
    console.warn('测试页面滚动到边界，恢复Canvas重绘')
  }, 150)
}
</script>

<template>
  <view class="test-page">
    <!-- 页面标题 -->
    <PageHeader
      title="开发测试页面"
      @back="goHome"
    />

    <!-- 动态背景 - 保留原有测试效果 -->
    <view class="test-background">
      <view class="color-block block-1" />
      <view class="color-block block-2" />
      <view class="color-block block-3" />
      <view class="color-block block-4" />
    </view>

    <!-- 主滚动容器 -->
    <scroll-view
      class="main-scroll-container"
      scroll-y
      :bounces="false"
      :show-scrollbar="false"
      :enable-passive="true"
      :scroll-with-animation="false"
      :style="{
        height: `calc(100vh - ${contentOffset12px})`,
        marginTop: contentOffset12px,
      }"
      @scroll="handleScroll"
      @scrolltoupper="handleScrollEnd"
      @scrolltolower="handleScrollEnd"
    >
      <view class="test-content">
        <!-- 环境切换区 -->
        <view class="test-section">
          <view class="section-title">
            环境切换
          </view>
          <view class="section-content">
            <EnvironmentSwitcher />
          </view>
        </view>

        <!-- 样式测试区 -->
        <view class="test-section">
          <view class="section-title">
            样式测试区
          </view>
          <view class="section-content">
            <!-- 毛玻璃效果测试卡片 -->
            <view class="test-cards">
              <view class="test-card">
                <text class="card-title">
                  毛玻璃卡片 1
                </text>
                <text class="card-content">
                  测试毛玻璃背景效果
                </text>
              </view>
              <view class="test-card">
                <text class="card-title">
                  毛玻璃卡片 2
                </text>
                <text class="card-content">
                  渐变背景 + 边框光晕
                </text>
              </view>
              <view class="test-card">
                <text class="card-title">
                  毛玻璃卡片 3
                </text>
                <text class="card-content">
                  多重阴影层次效果
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 主题测试区 -->
        <view class="test-section">
          <view class="section-title">
            主题测试区
          </view>
          <view class="section-content">
            <view class="theme-blocks">
              <view class="theme-block primary-theme">
                <text>主色调测试</text>
              </view>
              <view class="theme-block secondary-theme">
                <text>辅助色测试</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 五行色彩测试区 -->
        <view class="test-section">
          <view class="section-title">
            五行色彩系统
          </view>
          <view class="section-content">
            <view class="wuxing-colors">
              <view class="jin wuxing-item">
                <text>金</text>
              </view>
              <view class="wuxing-item mu">
                <text>木</text>
              </view>
              <view class="wuxing-item shui">
                <text>水</text>
              </view>
              <view class="wuxing-item huo">
                <text>火</text>
              </view>
              <view class="wuxing-item tu">
                <text>土</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 组件测试区 -->
        <view class="test-section">
          <view class="section-title">
            通用组件测试
          </view>
          <view class="section-content">
            <!-- GlassCard 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                GlassCard 毛玻璃卡片
              </text>
              <view class="demo-content">
                <GlassCard :blur="15" :opacity="0.3" class="demo-glass-card">
                  <text>默认毛玻璃效果</text>
                </GlassCard>
                <GlassCard
                  :blur="20"
                  :opacity="0.2"
                  :border-radius="20"
                  class="demo-glass-card variant-primary"
                >
                  <text>主题色变体</text>
                </GlassCard>
              </view>
            </view>

            <!-- StarRating 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                StarRating 星级评分
              </text>
              <view class="demo-content">
                <view class="rating-row">
                  <text>只读评分:</text>
                  <StarRating :rating="4.5" :show-score="true" />
                </view>
                <view class="rating-row">
                  <text>可交互评分:</text>
                  <StarRating
                    :rating="rating"
                    :readonly="false"
                    :show-score="true"
                    @change="handleStarRatingChange"
                  />
                </view>
                <view class="rating-row">
                  <text>不同尺寸:</text>
                  <view class="rating-sizes">
                    <StarRating :rating="3" size="small" />
                    <StarRating :rating="4" size="medium" />
                    <StarRating :rating="5" size="large" />
                  </view>
                </view>
              </view>
            </view>

            <!-- IconButton 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                IconButton 图标按钮
              </text>
              <view class="demo-content">
                <view class="button-row">
                  <text>按钮类型:</text>
                  <view class="button-group">
                    <IconButton
                      icon="icon-home"
                      type="primary"
                      @click="handleIconButtonClick('主要')"
                    />
                    <IconButton
                      icon="icon-user"
                      type="secondary"
                      @click="handleIconButtonClick('次要')"
                    />
                    <IconButton
                      icon="icon-heart"
                      type="danger"
                      @click="handleIconButtonClick('危险')"
                    />
                  </view>
                </view>
                <view class="button-row">
                  <text>按钮尺寸:</text>
                  <view class="button-group">
                    <IconButton icon="icon-settings" size="small" />
                    <IconButton icon="icon-settings" size="medium" />
                    <IconButton icon="icon-settings" size="large" />
                  </view>
                </view>
                <view class="button-row">
                  <text>圆形按钮:</text>
                  <view class="button-group">
                    <IconButton icon="icon-add" :round="true" />
                    <IconButton icon="icon-search" :round="true" type="success" />
                    <IconButton icon="icon-more" :round="true" type="warning" />
                  </view>
                </view>
                <view class="button-row">
                  <text>带文字按钮:</text>
                  <view class="button-group">
                    <IconButton icon="icon-download" text="下载" />
                    <IconButton icon="icon-share" text="分享" type="secondary" />
                  </view>
                </view>
              </view>
            </view>

            <!-- CategoryTabs 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                CategoryTabs 分类标签
              </text>
              <view class="demo-content">
                <CategoryTabs
                  :tabs="categoryTabs"
                  :active-key="currentTab"
                  @change="handleCategoryChange"
                />
                <text class="current-tab">
                  当前选中: {{ currentTab }}
                </text>
              </view>
            </view>

            <!-- LikeFavoriteButton 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                LikeFavoriteButton 交互按钮
              </text>
              <view class="demo-content">
                <view class="button-row">
                  <text>点赞按钮:</text>
                  <view class="button-group">
                    <LikeFavoriteButton type="like" :count="12" :active="false" />
                    <LikeFavoriteButton type="like" :count="23" :active="true" />
                  </view>
                </view>
                <view class="button-row">
                  <text>收藏按钮:</text>
                  <view class="button-group">
                    <LikeFavoriteButton type="favorite" :count="8" :active="false" />
                    <LikeFavoriteButton type="favorite" :count="15" :active="true" />
                  </view>
                </view>
                <view class="button-row">
                  <text>分享按钮:</text>
                  <view class="button-group">
                    <LikeFavoriteButton type="share" text="分享" />
                    <LikeFavoriteButton type="share" text="转发" size="small" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 业务组件测试区 -->
        <view class="test-section">
          <view class="section-title">
            业务组件测试
          </view>
          <view class="section-content">
            <!-- WeatherEnergySection 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                WeatherEnergySection 天气能量整合
              </text>
              <view class="demo-content">
                <WeatherEnergySection
                  :weather-data="weatherData"
                  :location-info="locationInfo"
                  :lunar-info="lunarInfo"
                  :energy-data="energyData"
                  :advice-list="adviceList"
                  @location-tap="handleLocationTap"
                  @energy-tap="handleEnergyTap"
                  @advice-tap="handleAdviceTap"
                />
              </view>
            </view>

            <!-- CitySelector 独立组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                CitySelector 城市选择器
              </text>
              <view class="demo-content">
                <view class="city-selector-test">
                  <text class="test-desc">
                    当前选择城市：{{ testSelectedCity }}
                  </text>
                  <CitySelector
                    v-model="testSelectedCity"
                    @change="handleTestCityChange"
                  />
                </view>
              </view>
            </view>

            <!-- OutfitRecommendationCard 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                OutfitRecommendationCard 推荐卡片
              </text>
              <view class="demo-content">
                <OutfitRecommendationCard
                  title="智能搭配推荐"
                  :rating="4.8"
                  :show-rating="true"
                >
                  <view class="recommendation-content">
                    <text>基于您的五行属性和今日天气，为您推荐以下搭配方案</text>
                  </view>
                  <template #header-right>
                    <IconButton icon="icon-more" type="ghost" size="small" />
                  </template>
                </OutfitRecommendationCard>
              </view>
            </view>

            <!-- CaseGrid 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                CaseGrid 案例网格
              </text>
              <view class="demo-content">
                <CaseGrid
                  :case-list="caseList"
                  @like="handleCaseLike"
                  @favorite="handleCaseFavorite"
                />
              </view>
            </view>

            <!-- WuxingCircle 组件测试 -->
            <view class="component-demo">
              <text class="demo-title">
                WuxingCircle 五行相生相克图
              </text>
              <view class="demo-content">
                <!-- 默认尺寸和完整功能 -->
                <view class="wuxing-demo-section">
                  <text class="wuxing-demo-subtitle">
                    默认效果（木旺缺水）
                  </text>
                  <WuxingCircle
                    :wuxing-data="wuxingTestData"
                    size="large"
                    :show-relations="true"
                    :show-labels="true"
                  />
                </view>

                <!-- 不同数据示例 -->
                <view class="wuxing-demo-section">
                  <text class="wuxing-demo-subtitle">
                    五行平衡（火为日主）
                  </text>
                  <WuxingCircle
                    :wuxing-data="wuxingTestData2"
                    size="large"
                    :show-relations="true"
                    :show-labels="true"
                  />
                </view>

                <!-- 不同尺寸测试 -->
                <view class="wuxing-demo-section">
                  <text class="wuxing-demo-subtitle">
                    不同尺寸对比
                  </text>
                  <view class="wuxing-sizes-row">
                    <view class="wuxing-size-item">
                      <text class="size-label">
                        小尺寸
                      </text>
                      <WuxingCircle
                        :wuxing-data="wuxingTestData"
                        size="small"
                        :show-relations="true"
                        :show-labels="true"
                      />
                    </view>
                    <view class="wuxing-size-item">
                      <text class="size-label">
                        大尺寸
                      </text>
                      <WuxingCircle
                        :wuxing-data="wuxingTestData"
                        size="large"
                        :show-relations="true"
                        :show-labels="true"
                      />
                    </view>
                  </view>
                </view>

                <!-- 简化版本 -->
                <view class="wuxing-demo-section">
                  <text class="wuxing-demo-subtitle">
                    简化版（无线条标注）
                  </text>
                  <WuxingCircle
                    :wuxing-data="wuxingTestData"
                    size="large"
                    :show-relations="false"
                    :show-labels="false"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 动画测试区 -->
        <view class="test-section">
          <view class="section-title">
            动画测试区
          </view>
          <view class="section-content">
            <view class="animation-demos">
              <view class="bounce-demo">
                <text>弹跳动画</text>
              </view>
              <view class="fade-demo">
                <text>渐显动画</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部安全区域 -->
        <view class="safe-area-bottom" />
      </view>
    </scroll-view>

    <!-- 浮动组件测试 -->
    <FloatingActionButton
      icon="icon-xiangji1"
      position="bottom-right"
      size="large"
      @click="handleFloatingClick"
    />

    <FloatingActionButton
      icon="icon-add"
      position="bottom-left"
      size="medium"
      background-color="rgba(34, 197, 94, 0.4)"
      @click="handleFloatingClick"
    />

    <!-- 开发工具 -->
    <DevTools
      position="top-right"
      @click="handleDevToolsClick"
    />
  </view>
</template>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  // 创建新的层级上下文，解决iOS层级问题
  isolation: isolate;

  // iOS触摸优化
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;

  // 确保根容器可以响应触摸
  touch-action: manipulation;
}

// 主滚动容器
.main-scroll-container {
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;

  // iOS触摸滚动优化
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;

  // 确保触摸事件正常传递
  pointer-events: auto;

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    -webkit-appearance: none !important;
  }

  ::-webkit-scrollbar-track {
    display: none !important;
    background: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    display: none !important;
    background: transparent !important;
  }

  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* 动态背景 - 保留原有效果 */
.test-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1; /* 使用负z-index确保始终在最底层 */

  // 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  // 防止背景干扰触摸事件
  pointer-events: none;

  // iOS触摸优化
  -webkit-user-select: none;
  user-select: none;
}

.color-block {
  position: absolute;
  border-radius: 20px;
}

.block-1 {
  width: 150px;
  height: 150px;
  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
  top: 10%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.block-2 {
  width: 120px;
  height: 120px;
  background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%);
  top: 30%;
  right: 15%;
  animation: float 8s ease-in-out infinite reverse;
}

.block-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);
  bottom: 25%;
  left: 20%;
  animation: float 7s ease-in-out infinite;
}

.block-4 {
  width: 130px;
  height: 130px;
  background: linear-gradient(45deg, #a8c8ec 0%, #7fcdff 100%);
  bottom: 15%;
  right: 10%;
  animation: float 9s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

/* 测试内容区域 */
.test-content {
  position: relative;
  z-index: 10; /* 提高z-index确保在背景层之上 */
  padding: 16px 16px 120px; /* 使用固定的内边距 */

  // 强制创建层级上下文，确保iOS兼容性
  transform: translateZ(0);
  -webkit-transform: translateZ(0);

  // 确保内容区域可交互
  pointer-events: auto;

  // iOS触摸优化
  -webkit-user-select: auto;
  user-select: auto;

  // 确保触摸事件传递
  touch-action: manipulation;
}

/* 测试区块 */
.test-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-content {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.25) 100%
  );
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);

  // 确保内容区域可交互
  pointer-events: auto;

  // iOS触摸优化
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* 毛玻璃测试卡片 */
.test-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.3) 100%
  );
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.card-content {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 组件测试样式 */
.component-demo {
  margin-bottom: 24px;
}

.demo-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
  display: block;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-glass-card {
  text-align: center;
  padding: 16px;
  margin-bottom: 8px;
}

.demo-glass-card text {
  color: white;
  font-size: 14px;
}

/* StarRating 测试样式 */
.rating-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.rating-row text {
  color: white;
  font-size: 14px;
  min-width: 80px;
}

.rating-sizes {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-tab {
  color: white;
  font-size: 14px;
  margin-top: 8px;
  display: block;
}

/* IconButton 测试样式 */
.button-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  flex-wrap: wrap;
}

.button-row text {
  color: white;
  font-size: 14px;
  min-width: 80px;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 业务组件测试样式 */
.recommendation-content {
  padding: 16px;
  text-align: center;
}

.recommendation-content text {
  color: var(--color-text-secondary, #6b7280);
  font-size: 12px;
  line-height: 1.5;
}

/* 主题测试区 */
.theme-blocks {
  display: flex;
  gap: 12px;
}

.theme-block {
  flex: 1;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
}

.primary-theme {
  background: linear-gradient(135deg, $primary-color, $primary-light);
}

.secondary-theme {
  background: linear-gradient(135deg, $success-color, #34d399);
}

/* 五行色彩测试 */
.wuxing-colors {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.wuxing-item {
  flex: 1;
  min-width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.jin {
  background: $wuxing-jin-gradient;
  color: $text-primary;
  text-shadow: none;
}

.mu {
  background: $wuxing-mu-gradient;
}

.shui {
  background: $wuxing-shui-gradient;
}

.huo {
  background: $wuxing-huo-gradient;
}

.tu {
  background: $wuxing-tu-gradient;
}

/* 组件测试占位 */
.component-placeholder {
  padding: 40px 20px;
  text-align: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* CitySelector 测试样式 */
.city-selector-test {
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-desc {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

/* 动画测试区 */
.animation-demos {
  display: flex;
  gap: 16px;
}

.bounce-demo {
  flex: 1;
  padding: 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-align: center;
  color: white;
  animation: bounce 2s infinite;
}

.fade-demo {
  flex: 1;
  padding: 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-align: center;
  color: white;
  animation: fadeInOut 3s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* WuxingCircle 测试样式 */
.wuxing-demo-section {
  margin-bottom: 32px;
  padding: 16px 0 24px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 300px; /* 确保足够高度显示large尺寸的组件和图例 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wuxing-demo-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.wuxing-demo-subtitle {
  display: block;
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
}

.wuxing-sizes-row {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  width: 100%;
  margin-top: 8px;
}

.wuxing-size-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  min-height: 300px; /* 确保对比展示区域有足够高度 */
}

.size-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 120px;
}

/* iOS专用触摸优化 */
/* 确保所有按钮和可交互元素在iOS上正常工作 */
button,
.demo-glass-card,
.test-card,
.theme-block,
.wuxing-item,
.bounce-demo,
.fade-demo {
  // iOS触摸优化
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  user-select: none;
  touch-action: manipulation;

  // 确保可交互元素有正确的指针事件
  pointer-events: auto;

  // iOS触摸延迟优化
  -webkit-touch-callout: none;
  -webkit-appearance: none;

  // 改善触摸响应
  cursor: pointer;
}

/* 特别优化文本选择区域 */
text,
.section-title,
.demo-title,
input,
textarea {
  -webkit-user-select: auto;
  user-select: auto;
  pointer-events: auto;
}
</style>
