<!-- 五行分析组件 -->
<script setup lang="ts">
import { computed } from 'vue'
import WuxingCircle from '@/components/business/WuxingCircle.vue'

// 五行数据类型定义
interface WuxingElement {
  element: '金' | '木' | '水' | '火' | '土'
  percentage: number
  isRizhu?: boolean // 是否为日主
}

interface WuxingAnalysisData {
  elements: WuxingElement[]
  analysis?: string
}

// Props定义
interface Props {
  wuxingData?: WuxingAnalysisData
}

const props = withDefaults(defineProps<Props>(), {})

// 转换数据格式给WuxingCircle组件使用
const wuxingCircleData = computed(() => {
  if (!props.wuxingData?.elements) {
    return []
  }

  return props.wuxingData.elements.map(item => ({
    element: item.element,
    percentage: item.percentage,
    isHighlight: item.isRizhu, // 日主高亮显示
  }))
})

// 获取分析文本
const analysisText = computed(() => {
  return props.wuxingData?.analysis || '五行分析数据加载中...'
})
</script>

<template>
  <view class="wuxing-analysis-card">
    <!-- 标题 -->
    <view class="card-header">
      <text class="card-title">
        五行分析
      </text>
      <text class="card-subtitle">
        个人五行分布与特征
      </text>
    </view>

    <!-- 五行圆形图 -->
    <view v-if="wuxingCircleData.length > 0" class="wuxing-circle-container">
      <WuxingCircle
        :wuxing-data="wuxingCircleData"
        size="large"
        :show-relations="true"
        :show-labels="true"
      />
    </view>

    <!-- 无数据状态 -->
    <view v-else class="no-data-container">
      <text class="no-data-text">
        暂无五行数据
      </text>
    </view>

    <!-- 分析文本 -->
    <view v-if="analysisText" class="analysis-text-container">
      <text class="analysis-text">
        {{ analysisText }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.wuxing-analysis-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  // 🔑 确保正确的层级和渲染上下文
  position: relative;
  z-index: 1;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.card-header {
  text-align: center;
  margin-bottom: 24px;
}

.card-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.card-subtitle {
  display: block;
  font-size: 12px;
  color: #666;
}

.wuxing-circle-container {
  min-height: 350px; /* 确保足够高度显示large尺寸的Canvas组件 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;

  // 🔑 确保Canvas容器有正确的层级
  position: relative;
  z-index: 2;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-text {
  color: #666;
  font-size: 14px;
}

.analysis-text-container {
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.analysis-text {
  font-size: 14px;
  line-height: 1.6;
  color: #444;
  text-align: justify;
}
</style>
